<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Hoclieu.HttpApi.Host</name>
    </assembly>
    <members>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AdaptiveTestController">
            <summary>
            Controller <PERSON><PERSON> lý các API liên quan đến bài kiểm tra trình độ
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AdaptiveTestController.#ctor(Hoclieu.Users.StudentRepository,Hoclieu.Books.BookRepository,Hoclieu.Books.ChapterRepository,Hoclieu.AdaptiveTests.AdaptiveTestResultRepository,Hoclieu.AdaptiveTests.AdaptiveTestQuestionCacheRepository,Hoclieu.AdaptiveTests.AdaptiveTestAnsweredQuestionRepository,Hoclieu.Books.EstimateThetaBookUserRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository,Hoclieu.DataQuestions.SkillTemplateDataRepository,Hoclieu.Services.DataQuestionService,Hoclieu.Services.SkillService,Hoclieu.Services.CAT.CATService,Hoclieu.Mongo.Service.MongoQuestionRepository,Hoclieu.Mongo.Service.MongoSkillResultRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheRepository,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Đây là hàm khởi tạo
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AdaptiveTestController.GetQuestionAdaptiveTest(System.Guid)">
            <summary>
            Lấy câu hỏi cho bài kiểm tra trình độ
            </summary>
            <param name="bookId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AdaptiveTestController.Answer(System.Guid,Hoclieu.Skills.AnswerRequest)">
            <summary>
            trả lời câu hỏi
            </summary>
            <param name="bookId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AdaptiveTestController.GetSkillSuggestFromAdaptiveTestResult(System.Guid,System.String,System.Int32,System.Int32)">
            <summary>
            API lấy kỹ năng gợi ý cho học sinh
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AddressUser.AddressUserController.GetByTenantUserId(System.Int64)">
            <summary>
            Lấy tất cả địa chỉ theo TenantUserId
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AddressUser.AddressUserController.GetById(System.Guid)">
            <summary>
            Lấy thông tin chi tiết một địa chỉ
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AddressUser.AddressUserController.Create(Hoclieu.Core.Dtos.AddressUser.AddressUserDto)">
            <summary>
            Tạo mới một địa chỉ
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AddressUser.AddressUserController.Update(System.Guid,Hoclieu.Core.Dtos.AddressUser.AddressUserDto)">
            <summary>
            Cập nhật một địa chỉ
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AddressUser.AddressUserController.Delete(System.Guid)">
            <summary>
            Xóa một địa chỉ
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AnalyticsController">
            <summary>
            API phục vụ cho viêc thống kê báo cáo
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Skills.SkillRepository,Hoclieu.Books.ChapterRepository,Hoclieu.Mongo.Service.MongoAnsweredQuestionRepository,Hoclieu.Services.AnalyticService,Hoclieu.Users.TeacherRepository,Hoclieu.Users.StudentRepository,Hoclieu.Services.ClassroomService,Hoclieu.Books.BookRepository,Hoclieu.Mongo.Service.MongoSkillResultRepository,Hoclieu.Books.SectionGameRepository,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Hoclieu.Mongo.Service.MongoQuestionRepository,Hoclieu.Mongo.Service.MongoGroupContentRepository,Microsoft.Extensions.Options.IOptions{Hoclieu.Services.Settings.K12OnlineSettings})">
            <summary>
            Constructor
            </summary>
            <param name="context"></param>
            <param name="skillRepository"></param>
            <param name="chapterRepository"></param>
            <param name="answeredQuestionRepository"></param>
            <param name="analyticService"></param>
            <param name="teacherRepository"></param>
            <param name="studentRepository"></param>
            <param name="classroomService"></param>
            <param name="bookRepository"></param>
            <param name="skillResultRepository"></param>
            <param name="sectionGameRepository"></param>
            <param name="userManager"></param>
            <param name="mongoQuestionRepository"></param>
            <param name="groupContentRepository"></param>
            <param name="k12OnlineSettings"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.Get(System.Guid)">
            <summary>
            Lấy ra thông tin câu hỏi đã trả lời
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.TroubleQuestion(Hoclieu.Core.Dtos.Analytics.GetTroubleQuestionRequest)">
            <summary>
            Lấy ra thông tin câu hỏi đã trả lời
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetUsageDetail(Hoclieu.Analytics.GetAnalyticsRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetUsageDetailStudent(System.Guid,Hoclieu.Analytics.GetAnalyticsRequest)">
             <summary>
            
             </summary>
             <param name="studentId"></param>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.RecentSkills(Hoclieu.Analytics.GetAnalyticsRequest)">
            <summary>
            API lấy ra thông tin về kỹ năng được trả lời nhiều nhất theo môn học và lớp
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.RecentSkills(System.Guid,Hoclieu.Analytics.GetAnalyticsRequest)">
            <summary>
            Lấy ra thông tin về kỹ năng được trả lời nhiều nhất theo môn học và lớp của học sinh được chọn
            </summary>
            <param name="studentId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetTroubleSpots(Hoclieu.Analytics.GetAnalyticsRequest,System.Guid)">
            <summary>
             Lấy ra thông tin về kỹ năng được trả sai theo môn học và lớp của học sinh được chọn
            </summary>
            <param name="request"></param>
            <param name="classroomId"></param>
            <returns></returns>
            <exception cref="T:System.AccessViolationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetTroubleSpotsByUser(Hoclieu.Analytics.GetAnalyticsRequest,System.Guid)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <param name="userId"></param>
             <returns></returns>
             <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetQuestionLog(System.Guid)">
             <summary>
            
             </summary>
             <param name="skillId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetQuestionLog(System.Guid,System.Guid)">
             <summary>
            
             </summary>
             <param name="skillId"></param>
             <param name="studentId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
             <exception cref="T:System.AccessViolationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.RecentSkills">
            <summary>
            Lấy KTKN trả lời gần nhất
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetAggregaredAnsweredQuestionCountAsync(System.Int32,System.Guid)">
            <summary>
            Lấy số câu hỏi trả lời trong khoảng thời gian bởi từng học sinh trong một hoặc tất cả các lớp học của giáo viên
            </summary>
            <param name="days"></param>
            <param name="classroomId"></param>
            <returns></returns>
            <exception cref="T:System.AccessViolationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetAggregatedStudentsStatsAsync(System.Int32,System.Guid)">
            <summary>
            Lấy thống kê tổng cộng của các học sinh trong một hoặc tất cả các lớp học của giáo viên
            </summary>
            <param name="days"></param>
            <param name="classroomId"></param>
            <returns></returns>
            <exception cref="T:System.AccessViolationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetStudentStatistic(System.Guid,System.Int32)">
            <summary>
            Lấy thống kê của một học sinh
            </summary>
            <param name="studentId"></param>
            <param name="days"></param>
            <returns></returns>
            <exception cref="T:System.AccessViolationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetMyStatistic(System.Int32)">
            <summary>
            Lấy thống kê của bản thân
            </summary>
            <param name="days"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetSkillStatistic(System.Guid,System.Guid,System.Int32)">
            <summary>
            Lấy thống kê về một kỹ năng của các học sinh trong một hoặc tất cả các lớp học của giáo viên
            </summary>
            <param name="skillId"></param>
            <param name="classroomId"></param>
            <param name="days"></param>
            <returns></returns>
            <exception cref="T:System.AccessViolationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetAnswerStatistic(Hoclieu.Analytics.GetAnalyticsRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetAnswerStatistic(System.Guid,Hoclieu.Analytics.GetAnalyticsRequest)">
             <summary>
            
             </summary>
             <param name="studentId"></param>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetScoreGridTeacher(System.Guid,System.Guid)">
             <summary>
            
             </summary>
             <param name="bookId"></param>
             <param name="classroomId"></param>
             <returns></returns>
             <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetBookClassroomById(System.Guid)">
             <summary>
            
             </summary>
             <param name="bookId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetChapterClassroomById(System.Guid,System.Guid)">
             <summary>
            
             </summary>
             <param name="chapterId"></param>
             <param name="classroomId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetSkillResults(Hoclieu.Core.Dtos.Book.SkillBookDetailRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.AnalyticSkill(System.Guid,System.Nullable{System.Guid})">
            <summary>
            Skill Detail
            </summary>
            <param name="skillId"></param>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.AnalyticSkillPercent(System.Guid,System.Nullable{System.Guid})">
            <summary>
            skill detail percent
            </summary>
            <param name="skillId"></param>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.AnalyticSectionGame(System.Guid,System.Nullable{System.Guid})">
             <summary>
            
             </summary>
             <param name="sectionGameId"></param>
             <param name="classroomId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.AnalyticSectionGamePercent(System.Guid,System.Nullable{System.Guid})">
             <summary>
            
             </summary>
             <param name="sectionGameId"></param>
             <param name="classroomId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetBookDetailRankTeacher(System.Guid,System.Nullable{System.Guid})">
            <summary>
            API lấy thông tin xếp hạng sách
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetReportNgayhevui(System.Guid,Hoclieu.Core.Dtos.NgayHeVui.GetReportNgayhevuiRequest)">
             <summary>
             API thống kê báo bao cao nhat ky he
             </summary>
             <param name="bookId"></param>
             <param name="request"></param>
             <returns></returns>
            
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnalyticsController.GetReportNgayhevuiStudent(System.Guid,System.Guid,Hoclieu.Core.Dtos.NgayHeVui.GetReportNgayhevuiStudentRequest)">
             <summary>
             API thống kê báo bao cao nhat ky he
             </summary>
             <param name="bookId"></param>
             <param name="userId"></param>
             <param name="request"></param>
             <returns></returns>
            
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AnsweredQuestion.AnsweredQuestionsController">
            <summary>
            AnsweredQuestion
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnsweredQuestion.AnsweredQuestionsController.#ctor(Hoclieu.Mongo.Service.MongoAnsweredQuestionRepository,Hoclieu.Mongo.Service.MongoSuggestion.MongoSuggestionRepository,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            AnsweredQuestionController
            </summary>
            <param name="answeredQuestionRepository"></param>
            <param name="suggestionRepository"></param>
            <param name="context"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnsweredQuestion.AnsweredQuestionsController.GetAnsweredQuestionBySkillId(Hoclieu.AnsweredQuestions.AnswerQuestionRequest)">
            <summary>
            Lấy danh sách answeredQuestion với bài tự luận đã được giao cho giáo viên
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AnsweredQuestion.AnsweredQuestionsController.CheckLockQuestion(Hoclieu.AnsweredQuestions.AnswerQuestionRequest)">
            <summary>
            Kiểm tra xem câu hỏi có bị khóa không
            </summary>
            <param name="request">Yêu cầu kiểm tra khóa câu hỏi</param>
            <returns>True nếu bị khóa, ngược lại false</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AppVersionController">
            <summary>
            Quản lý phiên bản ứng dụng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AppVersionController.#ctor(Hoclieu.Users.AppVersionRepository)">
            <summary>
            Hàm khởi tạo controller app version
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AppVersionController.GetVersion(System.Nullable{System.Guid})">
            <summary>
            Lấy phiên bản mới nhất theo bộ lọc
            </summary>
            <param name = "publisherId" >Id nhà xuất bản</param>
            <returns>Thông tin phiên bản</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AppVersionController.AddVersion(Hoclieu.Dtos.CreateVesionRequest)">
            <summary>
            Tạo mới phiên bản
            </summary>
            <param name = "request" >Dữ liệu tạo mới phiên bản</param>
            <returns>Thông tin phiên bản</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AppVersionController.UpdateVersion(System.Guid,Hoclieu.Dtos.CreateVesionRequest)">
            <summary>
            Sửa phiên bản
            </summary>
            <param name = "id" >Id phiên bản</param>
            <param name = "request" >Dữ liệu sửa phiên bản</param>
            <returns>Thông tin phiên bản</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AppVersionController.DeleteVersion(System.Guid)">
            <summary>
            Xóa phiên bản
            </summary>
            <param name = "id" >Id phiên bản</param>
            <returns>Thông tin phiên bản</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AppVersionController.GetVersionK8S">
            <summary>
            Lấy phiên bản hiện tại đang chạy trên k8s
            </summary>
            <returns>Thông tin phiên bản</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetenciesController">
            <summary>
            Controller for assessment competencies
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetenciesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Controller for assessment competencies
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetenciesController.GetAll">
            <summary>
            Get all assessment competencies
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetenciesController.Get(System.Int32)">
            <summary>
            Get an assessment competency by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetenciesController.Create(Hoclieu.Core.Dtos.Assessments.AssessmentCompetencyDto)">
            <summary>
            Create a new assessment competency
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetenciesController.Update(System.Int32,Hoclieu.Core.Dtos.Assessments.AssessmentCompetencyDto)">
            <summary>
            Update an assessment competency by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetenciesController.Delete(System.Int32)">
            <summary>
            Delete an assessment competency by id
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetencyStudentsController">
            <summary>
            Controller for assessment competency students
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetencyStudentsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Controller for assessment competency students
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetencyStudentsController.GetAll">
            <summary>
            Get all assessment competency students
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetencyStudentsController.Get(System.Int64)">
            <summary>
            Get an assessment competency student by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetencyStudentsController.Create(Hoclieu.Core.Dtos.Assessments.AssessmentCompetencyStudentDto)">
            <summary>
            Update or insert assessment competency student
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetencyStudentsController.Update(System.Int64,Hoclieu.Core.Dtos.Assessments.AssessmentCompetencyStudentDto)">
            <summary>
            Update an assessment competency student by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentCompetencyStudentsController.Delete(System.Int64)">
            <summary>
            Delete an assessment competency student by id
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AssessmentQualitiesController">
            <summary>
            Controller for assessment qualities
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualitiesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Controller for assessment qualities
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualitiesController.GetAll">
            <summary>
            Get all assessment qualities
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualitiesController.Get(System.Int32)">
            <summary>
            Get an assessment quality by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualitiesController.Create(Hoclieu.Core.Dtos.Assessments.AssessmentQualityDto)">
            <summary>
            Create a new assessment quality
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualitiesController.Update(System.Int32,Hoclieu.Core.Dtos.Assessments.AssessmentQualityDto)">
            <summary>
            Update an assessment quality by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualitiesController.Delete(System.Int32)">
            <summary>
            Delete an assessment quality by id
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AssessmentQualityStudentsController">
            <summary>
            Controller for assessment quality students
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualityStudentsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Controller for assessment quality students
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualityStudentsController.GetAll">
            <summary>
            Get all assessment quality students
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualityStudentsController.Get(System.Int64)">
            <summary>
            Get an assessment quality student by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualityStudentsController.Create(Hoclieu.Core.Dtos.Assessments.AssessmentQualityStudentDto)">
            <summary>
            Update or insert assessment quality student
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualityStudentsController.Update(System.Int64,Hoclieu.Core.Dtos.Assessments.AssessmentQualityStudentDto)">
            <summary>
            Update an assessment quality student by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentQualityStudentsController.Delete(System.Int64)">
            <summary>
            Delete an assessment quality student by id
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController">
             <summary>
            
             </summary>
             <remarks>
             Constructor
             </remarks>
             <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.AspNetCore.Http.IHttpContextAccessor)">
             <summary>
            
             </summary>
             <remarks>
             Constructor
             </remarks>
             <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.GetAssessmentSemesters">
            <summary>
            Lấy danh sách học kỳ đánh giá
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.CreateAssessmentSemester(Hoclieu.Core.Dtos.Assessments.AssessmentSemesterDto)">
            <summary>
            Tạo học kỳ đánh giá
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.UpdateAssessmentSemester(System.Int32,Hoclieu.Core.Dtos.Assessments.AssessmentSemesterDto)">
            <summary>
            Cập nhật học kỳ đánh giá
            </summary>
            <param name="id"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.DeleteAssessmentSemester(System.Int32)">
            <summary>
            Xóa học kỳ đánh giá
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.GetAssessmentSemesterResult(System.Int32)">
            <summary>
            Lấy danh sách đánh giá theo kỳ
            </summary>
            <param name="assessmentSemesterId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.GetAssessmentSemesterResultBySubject(System.Guid,System.Guid,Hoclieu.Core.Enums.AssessmentSemester.AssessmentSemesterType)">
             <summary>
            
             </summary>
             <param name="classRoomId"></param>
             <param name="subjectId"></param>
             <param name="semesterType"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.UpsertSemesterResultSubject(Hoclieu.Core.Dtos.Assessments.UpsertAssessmentSemesterResultRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSemestersController.GetAssessmentSubjectFilter(System.Guid)">
             <summary>
            
             </summary>
             <param name="classroomId"></param>
             <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AssessmentSubjectStudentsController">
            <summary>
            Controller for assessment subject students
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSubjectStudentsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Controller for assessment subject students
            </summary>
            <param name="hoclieuDbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSubjectStudentsController.GetAll">
            <summary>
            Get all assessment subject students
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSubjectStudentsController.Get(System.Int64)">
            <summary>
            Get an assessment subject student by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSubjectStudentsController.Create(Hoclieu.Core.Dtos.Assessments.AssessmentSubjectStudentDto)">
            <summary>
            Update or insert assessment subject student
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSubjectStudentsController.Update(System.Int64,Hoclieu.Core.Dtos.Assessments.AssessmentSubjectStudentDto)">
            <summary>
            Update an assessment subject student by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AssessmentSubjectStudentsController.Delete(System.Int64)">
            <summary>
            Delete an assessment subject student by id
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AudioController">
            <summary>
            Quản lý âm thanh
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AudioController.#ctor(Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings},Hoclieu.Services.FileService,Hoclieu.Services.T2SService)">
            <summary>
            Hàm khởi tạo controller audio
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AudioController.CreateAudio(Hoclieu.Core.Dtos.Audio.CreateAudioRequest)">
            <summary>
            Tạo âm thanh tự động từ google sheet
            </summary>
            <param name = "request" >Thông tin tạo âm thanh</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.Login(Hoclieu.Dtos.Auth.LoginRequest,System.Nullable{Hoclieu.Core.Enums.LoginProductType})">
            <summary>
            Đăng nhập.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.Register(Hoclieu.Dtos.Auth.RegisterRequest)">
            <summary>
            Đăng ký.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RegisterFirstStep(Hoclieu.Dtos.Auth.RegisterRequest)">
            <summary>
            Đăng ký bước 1.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.ChangePassword(Hoclieu.Dtos.Auth.ChangePasswordRequest)">
            <summary>
            Đổi mật khẩu.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.ForgotPassword(Hoclieu.Dtos.Auth.ForgotPasswordRequest)">
            <summary>
            Quên mật khẩu dùng email hoặc tên đăng nhập.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.GoogleLogin(Hoclieu.Dtos.Auth.GoogleLoginRequest,Hoclieu.Core.Enums.Auths.GoogleLoginType,System.Nullable{Hoclieu.Core.Enums.LoginProductType})">
            <summary>
            Đăng nhập bằng tài khoản Google.
            </summary>
            <param name="request">Thông tin đăng nhập Google.</param>
            <param name="type">Loại đăng nhập Google (Web hoặc Mobile).</param>
            <param name="source">Nguồn đăng nhập (mặc định là Hoclieu).</param>
            <returns>Thông tin đăng nhập người dùng.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.AuthenticateWithApple(Hoclieu.Core.Dtos.Auth.AppleLoginRequest)">
            <summary>
            Đăng nhập apple mobile
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.ZaloLogin(Hoclieu.Dtos.Auth.ZaloLoginRequest,System.Nullable{Hoclieu.Core.Enums.LoginProductType})">
            <summary>
            Đăng nhập bằng tài khoản Zalo.
            </summary>
            <param name="request">Thông tin đăng nhập Zalo.</param>
            <param name="source">Nguồn đăng nhập (mặc định là Hoclieu).</param>
            <returns>Thông tin đăng nhập người dùng.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.AuthenticateWithK12Online(Hoclieu.Core.Dtos.Auth.K12OnlineLoginRequest)">
            <summary>
            Login with K12Online token
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.FaceboookLogin(Hoclieu.Core.Dtos.Auth.FacebookLoginRequest)">
            <summary>
            đăng nhập với facebook
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RefreshTokens(Hoclieu.Core.Dtos.Auth.RevokeTokenRequest)">
            <summary>
            Lấy accesstoken mới từ refreshtoken
            </summary>
            <param name="request">refresh token</param>
            <returns>Access token mới và refresh token mới(mobile lưu lại cái refresh token mới đế khi accesstoken hết hạn thì lấy lại)</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RevokeTokens(Hoclieu.Core.Dtos.Auth.RevokeTokenRequest)">
            <summary>
            Thu hổi refresh token
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.GenerateJwtToken(Hoclieu.Users.ApplicationUser,System.Collections.Generic.IList{System.String})">
            <summary>
            Tạo accesstoken
            </summary>
            <param name="user">Dữ liệu người dùng</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.GenerateRefreshToken(System.String)">
            <summary>
            Tạo mã refresh token
            </summary>
            <param name="ipAddress">Địa chỉ api client</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.GetNewTokenResponse(Hoclieu.Users.ApplicationUser,System.String)">
            <summary>
            tạo mới accesstoken và refreshtoken
            </summary>
            <param name="user">Dữ liệu người dùng</param>
            <param name="ipAddress">Địa chỉ mạng máy khách</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RefreshToken(System.String,System.String)">
            <summary>
            Lấy accesstoken từ refreshtoken
            </summary>
            <param name="token">refreshtoken </param>
            <param name="ipAddress">Địa chỉ mạng máy khách</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RevokeToken(Hoclieu.Users.UserClaims,System.String,System.String)">
            <summary>
            vô hiệu hóa refreshtoken
            </summary>
            <param name="token">token được thu hồi</param>
            <param name="user">Dữ liệu người dùng</param>
            <param name="ipAddress">Địa chỉ mạng máy khác</param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RotateRefreshToken(Hoclieu.Domain.User.RefreshToken,System.String)">
            <summary>
            Tạo mã refresh mới từ mã cũ và ip mới
            </summary>
            <param name="refreshToken">mã cũ</param>
            <param name="ipAddress">địa chỉ ip</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RemoveOldRefreshTokens(System.Guid)">
            <summary>
            xóa các mã ko còn hoạt động và tạo được hơn 2 ngày
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RevokeDescendantRefreshTokens(Hoclieu.Domain.User.RefreshToken,System.Collections.Generic.List{Hoclieu.Domain.User.RefreshToken},System.String,System.String)">
            <summary>
            hàm đệ quy thu hồi các refreshtoken thay thế refresh đã hết hạn mà bị dùng lại
            </summary>
            <param name="refreshToken">Mã hết hạn</param>
            <param name="user"></param>
            <param name="ipAddress"></param>
            <param name="reason"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RevokeRefreshToken(Hoclieu.Domain.User.RefreshToken,System.String,System.String,System.String)">
            <summary>
            Thu hồi refresh token
            </summary>
            <param name="token">token muốn thu hồi</param>
            <param name="ipAddress">ip thực hiện thu hồi</param>
            <param name="reason">Nguyên nhân</param>
            <param name="replacedByToken">Token thay thế mới</param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.GetEmailByUsername(System.String)">
            <summary>
            Get email by username
            </summary>
            <param name="username"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RegisterByOtp(Hoclieu.Core.Dtos.Auth.RegisterOtpRequest)">
            <summary>
            Đăng ký bằng OTP
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.VerifyOtp(Hoclieu.Core.Dtos.Auth.VerifyOtpRequest)">
            <summary>
            Xác thực OTP với tài khoản mới
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.ForgotPasswordOtp(Hoclieu.Dtos.Auth.ForgotPasswordRequest)">
            <summary>
            Quên mật khẩu
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.VerifyForgotPasswordOtp(Hoclieu.Core.Dtos.Auth.VerifyForgotPasswordRequest)">
            <summary>
            Xac thực mã OTP quên mật khẩu
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AuthController.RefreshTokenForTenant">
            <summary>
            Refresh Token when tenant id change
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BannersController.Update(System.Guid,Hoclieu.Banners.CreateBannerRequest)">
            <summary>
            update banner
            </summary>
            <param name="id"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BannersController.Delete(System.Guid)">
            <summary>
            Xóa banner
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Book3SCodesController">
            <summary>
            Quản lý mã sách 3s
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Book3SCodesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Hàm khởi tạo controller book 3s codes
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Book3SCodesController.CheckSeri(System.String)">
            <summary>
            Kiểm tra seri
            </summary>
            <param name = "seri" >seri</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Book3SCodesController.CheckCode(System.String)">
            <summary>
            Kiểm tra mã kích hoạt
            </summary>
            <param name = "code" >mã kích hoạt</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Book3SCodesController.ImportCodes(System.String,System.String,System.String)">
            <summary>
            Import mã sách 3s
            </summary>
            <param name = "pathFile" ></param>
            <returns>Kết quả</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.BookCodesController">
            <summary>
            Quản lý kích hoạt sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Books.BookRepository,Hoclieu.Books.BookUserRepository,Hoclieu.Services.BookCodeService,Hoclieu.Services.BookCodeActivateHistoryService,Hoclieu.Services.FileService,Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings},AutoMapper.IMapper)">
            <summary>
            Hàm khởi tạo controller book code
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.CheckSerial(System.String,System.Boolean)">
            <summary>
            Kiểm tra trạng thái sách có số seri
            </summary>
            <param name="serial">Số seri</param>
            <param name="scanQr"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.ActivateBook(System.String,System.Guid,System.Nullable{System.Guid})">
            <summary>
            Kích hoạt sách với code
            </summary>
            <param name="code">Mã kích hoạt</param>
            <param name="bookId">Id sách</param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.ReactivateBook(System.String,System.Guid)">
            <summary>
            Đổi sách
            </summary>
            <param name="code">Mã kích hoạt sách</param>
            <param name="newBookId">Id sách mới</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.SelfActiveInfo(System.String,System.Nullable{System.Guid})">
            <summary>
            Lấy thông tin sách đã kích hoạt bởi người
            </summary>
            <param name="code">Mã kích hoạt</param>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.ActivetedInOtherWebInfo(System.String)">
            <summary>
            Lấy thông tin sách đã kích hoạt ở học liệu từ web ôn thi 10
            </summary>
            <param name="code">Mã kích hoạt</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.FindBookFromCode(System.String,Hoclieu.Books.FindBookFromCodeRequest,System.Boolean)">
            <summary>
            Tìm các sách có thể kích hoạt với code
            </summary>
            <param name="code">Mã kích hoạt</param>
            <param name="request">Dữ liệu bộ lọc</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.FindAvailableBookFromCode(System.String,Hoclieu.Books.FindBookFromCodeRequest)">
            <summary>
            Lấy danh sách lớp và môn học phù
            </summary>
            <param name="code">Mã kích hoạt</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.Import(System.String,System.String,System.Int32,System.String,System.Int32,System.Int32,System.String)">
            <summary>
            Nạp mã kích hoạt
            </summary>
            <param name="pathFile">Đường dẫn file*</param>
            <param name="region">Miền B T N</param>
            <param name="batch">Lô</param>
            <param name="expiryDateStr">Hạn</param>
            <param name="year">Năm*</param>
            <param name="bookType">Loại sách*</param>
            <param name="book">Mã sách</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.RecordCode(Hoclieu.Books.RecordCodeRequest)">
            <summary>
             log code error
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.GetBookCodeActivateHistoryByCode(System.String,System.Int32,System.Int32,System.Boolean,System.String)">
            <summary>
            Get book code activate history by code
            </summary>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.GetBookUser(System.String)">
             <summary>
            
             </summary>
             <param name="userNameOrEmail"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.CreateFakeBookWarning(System.String,Hoclieu.Domain.Books.CreateBookSerialFeedbackRequest)">
             <summary>
            
             </summary>
             <param name="serial"></param>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.GetFakeBookWarnings(Hoclieu.Core.Dtos.Book.GetBookCodeWarningsRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.GetAllEnglishBook">
            <summary>
            Lấy tất cả SGK Tiếng Anh
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookCodesController.CheckActiveBook(System.Guid)">
            <summary>
            Kiểm tra xem người dùng đã kích hoạt sách chưa
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.CategoryService,Hoclieu.Mongo.Service.MongoSkillQuestionStorageRepository)">
             <summary>
            
             </summary>
             <param name="context"></param>
             <param name="categoryService"></param>
             <param name="mongoSkillQuestionStorageRepository"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.BookDownloadStorage(System.Guid)">
            <summary>
            Lưu trữ dữ liệu sách để tải về
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.GetBookResourceLink(System.Guid)">
            <summary>
            Lấy danh sách cách link tài nguyên kỹ năng không hợp lệ trong sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.DownloadSkillQuestionStorage(System.Guid,System.Guid)">
            <summary>
            Tải về dữ liệu kỹ năng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.GetBookDownloadVersions(System.Guid)">
            <summary>
            Lấy tất cả thông tin phiên bản tải về
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.GetLatestVersion(System.Collections.Generic.List{System.Guid})">
             <summary>
            
             </summary>
             <param name="bookIds"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.ReleaseBookDownloadVersions(System.Guid)">
            <summary>
            Release sách
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.SetDataLinkBookDownloadVersions(System.Guid,System.String)">
             <summary>
            
             </summary>
             <param name="bookDownloadVersionId"></param>
             <param name="dataLink"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookDownloadsController.GetDataLinkBookDownload(Hoclieu.Core.Dtos.BookDownload.ListDataDownloadBookRequestDto)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookEditHistoryController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.BookEditHistoryService)">
            <summary>
            Hàm khởi tạo controller
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookEditHistoryController.GetAllBookEditHistory(Hoclieu.Books.GetBookEditHistoryRequest)">
            <summary>
            API lấy tất cả lịch sử tác động sách thỏa mãn.
            </summary>
            <param name="request"></param>
            <returns>List items và số lượng phần tử của list.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookGlossariesController.Search(System.Nullable{System.Guid},System.Nullable{System.Guid},System.Nullable{System.Guid},System.Nullable{System.Guid},Hoclieu.Core.Enums.Book.BookGlossaryType,System.Boolean)">
            <summary>
            Lấy danh sách các glossary được gán vào sách
            </summary>
            <param name="bookId">Định danh sách</param>
            <param name="chapterId">Định danh chương</param>
            <param name="lessonId">Định danh phần</param>
            <param name="sectionSkillId">Định danh bài</param>
            <param name="isGetDatail"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookGlossariesController.AllGlossaryBook(System.Guid)">
            <summary>
            Lấy danh sách tất cả glossary của sách
            </summary>
            <param name="bookId"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.BookGlossaryController">
            <summary>
            Class BookGlossaryController.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookGlossaryController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,AutoMapper.IMapper,Hoclieu.Services.BookService,Hoclieu.Services.DataQuestionService)">
            <summary>
            Initializes a new instance of the <see cref="T:Hoclieu.HttpApi.Host.Controllers.BookGlossaryController"/> class.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookGlossaryController.Create(Hoclieu.BookGlossaries.BookGlossaryCreateOrUpdate)">
            <summary>
            Create a new book glossary
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookGlossaryController.Update(System.Guid,Hoclieu.BookGlossaries.BookGlossaryCreateOrUpdate)">
            <summary>
            Update a book glossary
            </summary>
            <param name="id"></param>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookGlossaryController.Delete(System.Guid)">
            <summary>
            Delete a book glossary
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController">
            <summary>
            Class BookObjectiveController.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,AutoMapper.IMapper,Hoclieu.Services.BookService,Hoclieu.Services.DataQuestionService)">
            <summary>
            Initializes a new instance of the <see cref="T:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController"/> class.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController.Create(Hoclieu.Books.BookObjectiveCreateOrUpdate)">
            <summary>
            Create a new book glossary
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController.Update(System.Guid,Hoclieu.Books.BookObjectiveCreateOrUpdate)">
            <summary>
            Update a book glossary
            </summary>
            <param name="id"></param>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController.Delete(System.Guid)">
            <summary>
            Delete a book glossary
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController.GetObjectiveBySkillId(System.Guid)">
             <summary>
            
             </summary>
             <param name="skillId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookObjectiveController.GetObjectiveByChapterId(System.Guid)">
             <summary>
            
             </summary>
             <param name="chapterId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.BookPublicsController">
            <summary>
            API cho việc public sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookPublicsController.#ctor(Hoclieu.Mongo.Service.BookCSSPublicRepository,Hoclieu.Books.BookRepository,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.FileService,Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings})">
            <summary>
            constructor
            </summary>
            <param name="bookCssPublicRepository"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookPublicsController.AddBookPublicDraft(System.Guid,Hoclieu.Core.Dtos.Book.BookPublic.AddBookPublicDraftRequest)">
            <summary>
            Add book public draft
            </summary>
            <param name="bookId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookPublicsController.PublishBookCss(System.String)">
            <summary>
            Publish book css
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookPublicsController.GetBookCssPublic(System.Guid)">
            <summary>
            get book css public by bookId
            </summary>
            <param name="bookId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Books.BookRepository,Hoclieu.Books.BookCssRepository,Hoclieu.Books.BookUserRepository,Hoclieu.Services.BookService,Hoclieu.Services.BookCodeService,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Hoclieu.Users.EditorRepository,Hoclieu.Users.EditorBookAccessRepository,Hoclieu.Users.EditorGradeSubjectRepository,Hoclieu.Books.SkillGameRepository,Hoclieu.Skills.SkillRepository,Hoclieu.Books.ChapterRepository,Hoclieu.Checkpoints.CheckpointRepository,Hoclieu.Books.LessonGoalRepository,Hoclieu.Books.BookAnswerRepository,Hoclieu.Books.SectionSkillRepository,AutoMapper.IMapper,Hoclieu.AdaptiveTests.AdaptiveTestResultRepository,Hoclieu.Users.StudentRepository,Hoclieu.Users.TeacherRepository,Hoclieu.Mongo.Service.StudyTrackingRepository,Hoclieu.Services.SkillService,Hoclieu.Mongo.Service.MongoQuestionCacheRepository,Hoclieu.Books.BookEditHistoryRepository,Hoclieu.Mongo.Service.MongoSkillResultRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository,Hoclieu.Mongo.Service.BookCSSPublicRepository,Hoclieu.Mongo.Service.Worksheet.WorksheetRepository)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.Get(Hoclieu.Books.GetBooksRequest)">
            <summary>
            Lấy tất cả các sách theo bộ lọc
            </summary>
            <param name="request">Dữ liệu bộ lọc</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.LockBookDownload(System.Guid,Hoclieu.Core.Enums.LockDownloadBookStatus)">
            <summary>
            Thay đổi trạng thái khoá tải xuống sách
            </summary>
            <param name="bookId">Định danh sách</param>
            <param name="status">Trạng thái khoá</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetBookOption(Hoclieu.Books.GetBooksRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.Create(Hoclieu.Books.CreateBookRequest)">
            <summary>
            Tao moi sach
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetById(System.Guid,System.String)">
            <summary>
            Lấy thông tin chi tiết sách
            </summary>
            <param name="id">Id sách</param>
            <param name="pathName"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.UserGetById(System.Guid,System.String,System.Boolean)">
            <summary>
            Lấy thông tin chi tiết sách
            </summary>
            <param name="id">Id sách</param>
            <param name="pathName"></param>
            <param name="getFullResult"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetInfoById(System.Guid)">
            <summary>
            Lấy thông tin chi tiết sách
            </summary>
            <param name="id">Id sách</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.UpdateBook(System.Guid,Hoclieu.Books.UpdateBookRequest)">
            <summary>
            Cập nhật thông tin sách
            </summary>
            <param name="id">Id sách</param>
            <param name="request">Dữ liệu cập nhật sách</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.Find(Hoclieu.Books.FindBookRequestWithPublisherId)">
            <summary>
            Tìm sách theo tên, khối, môn học, nhà xuất bản
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetAllBookBySkill(System.Guid)">
            <summary>
            Lấy danh sách sách theo skillId
            </summary>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.FindBookSkill(Hoclieu.Books.FindSkillBookRequest)">
            <summary>
            find-book-skill
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetMyBooks(Hoclieu.Books.FindBookRequest)">
            <summary>
            Lấy danh sách các sách đã kích hoạt
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetBookByCheckpoint(System.Nullable{System.Guid},System.Nullable{System.Guid})">
            <summary>
            Lấy sách luyện thi
            </summary>
            <param name="checkpointId">định danh đề(Lấy sách theo định checkpoint)</param>
            <param name="bookHdotId">Định danh HDOT(lấy các sách CLT cùng khối và môn với sách HDOT)</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetBookCheckpointByUserId(System.Guid)">
            <summary>
            Lấy sách luyện thi theo user id
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetInfoBookByCheckpointId(System.Nullable{System.Guid})">
            <summary>
            Lấy sách bởi idCheckpoint
            </summary>
            <param name="checkpointId">định danh đề(Lấy sách theo định checkpoint)</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetBookDetailBySkill(System.Guid,System.Nullable{System.Guid})">
            <summary>
            get book detail by skillId
            </summary>
            <param name="skillId"></param>
            <param name="lessonId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetChaptersDetailByBook(System.Guid)">
            <summary>
            get chapters detail by bookId
            </summary>
            <param name="bookId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetBookPublishers(System.Guid)">
            <summary>
            Lấy danh sách id nhà xuất bản của sách
            </summary>
            <param name="bookId">Id sách</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.CreateBookPublishers(System.Guid,Hoclieu.Core.Dtos.Publisher.CreateBookPublishersRequest)">
            <summary>
            Cập nhật nhà xuất bản của sách
            </summary>
            <param name="bookId">Id sách</param>
            <param name="request">Danh sách id nhà xuất bản</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.CheckTryAdaptiveTest(System.Guid)">
            <summary>
            Kiểm tra lượt dùng thử adaptive test
            </summary>
            <param name="bookId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.CheckRoleBookFromBookId(System.Guid,System.String)">
            <summary>
            Kiểm tra người dùng có sở hưu sách hay không
            </summary>
            <param name="bookId">Định danh kĩ năng</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.BookRelationships(System.Guid)">
            <summary>
            lấy dữ liệu quan hệ sách
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.AddBookRelationships(System.Guid,Hoclieu.Core.Dtos.Book.UpdateBookRelationshipRequest,Hoclieu.Core.Enums.Book.BookRelationshipType)">
            <summary>
            Hàm gán tham chiếu sách giữa cổng luyện thi và HDOT
            </summary>
            <param name="id">Định danh sách tham chiếu </param>
            <param name="request">Định danh sách được tham chiếu </param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.UpdateAnsweredQuestion(System.Nullable{System.Guid})">
            <summary>
            Cập nhật số câu hỏi đã được trả lời cùa sách
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.UpdateNumericalOrderBook(System.Guid,System.Int32)">
             <summary>
            
             </summary>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.UpdateBookInfo(System.Guid,Hoclieu.Books.UpdateCssBook)">
            <summary>
            Cập nhật thông tin của sách
            </summary>
            <param name="id">Id sách</param>
            <param name="request">Dữ liệu cập nhật</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GenerateSolveGDoc(System.Guid,System.Boolean)">
            <summary>
            Tạo gdoc tổng hợp dữ liệu từ dữ liệu của sách
            </summary>
            <param name="id">Book Id</param>
            <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
            <returns>kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GenerateImageFromData(System.Guid)">
            <summary>
            Lấy dữ liệu ảnh từ dữ liệu của sách
            </summary>
            <param name="id">Book Id</param>
            <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
            <returns>kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GenerateImageFromDataBook(System.Guid)">
            <summary>
            Lấy dữ liệu ảnh từ dữ liệu của sách
            </summary>
            <param name="id">Book Id</param>
            <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
            <returns>kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GenerateImageFromDataGroupSkillId(System.Guid,System.Boolean)">
            <summary>
            Lấy dữ liệu ảnh từ dữ liệu của sách
            </summary>
            <param name="id">Book Id</param>
            <param name="ignoreTeacher">Bỏ qua mẫu chưa Teacher không</param>
            <param name="isGetImage"></param>
            <returns>kết quả</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.BooksController.CheckPermissionDoSectionGame(System.Guid)" -->
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetSectionGameKhoGame(System.Guid,Hoclieu.Core.Enums.Book.GameType)">
            <summary>
            Get list section game has type KhoGame by book id
            </summary>
            <param name="bookId"></param>
            <param name="gameType"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetBookPassNeedLogin(System.Guid)">
            <summary>
            Lấy thông tin sách có cần đăng nhập không
            </summary>
            <param name="id">Id sách</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetSkillPassNeedLogin(System.Guid,System.Nullable{System.Guid})">
            <summary>
            Lấy thông tin Skill có trong bộ sách cần đăng nhập không
            </summary>
            <param name="id">Id sách</param>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetStudentNoBuyBooks(System.Guid,Hoclieu.Books.FindStudentNoBuyBookRequest)">
            <summary>
            Kiểm trả xem sách trả phí đó học sinh nào chưa mua
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetTableOfContent(System.Guid)">
            <summary>
            Hiển thị mục lục sách cho trang PDF
            </summary>
            <param name="bookId"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetTagNameBook" -->
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.CreateTagNameBooks(System.Collections.Generic.List{Hoclieu.Books.CreateTagNameRequest})">
            <summary>
            Tạo list tag name book
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.UpdateTagBook(System.Int32,Hoclieu.Books.CreateTagNameRequest)">
            <summary>
            Sửa tag book
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.DeleteTagName(System.Int32)">
            <summary>
            Xoá tag book
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetAllBookByType(System.Collections.Generic.List{System.Int32})">
            <summary>
            Lấy sách theo bookType
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BooksController.GetLessonResult(System.Guid)">
            <summary>
            Get Lesson result
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.BookSerialManagementsController">
            <summary>
            Controller for book serial management
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookSerialManagementsController.#ctor(Hoclieu.EntityFrameworkCore.BookCodeDbContext,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Constructor for BookSerialManagementsController
            </summary>
            <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookSerialManagementsController.CreateOrUpdateBookSerialManagement(Hoclieu.Core.Dtos.Book.BookSerialManagement.BookSerialManagementDto)">
            <summary>
            Create or update a book serial management
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookSerialManagementsController.DeleteBookSerialManagement(System.Int32)">
            <summary>
            Delete a book serial management
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookSerialManagementsController.GetBookSerialManagements(Hoclieu.Core.Enums.Book.BookSerialManagementType,System.String,System.String,Hoclieu.Dtos.PagedAndSortedResultRequest)">
            <summary>
            Get list of book serial management filtered by type and return as a page
            </summary>
            <param name="type"></param>
            <param name="code"></param>
            <param name="input"></param>
            <param name="serial"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookSerialManagementsController.GetSettingCheckSerial">
            <summary>
            get setting check serial
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BookSerialManagementsController.UpdateSettingCheckSerial(Hoclieu.Core.Dtos.Book.BookSerialManagement.SettingCheckSerialDto)">
            <summary>
            update setting check serial
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ChaptersController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ChaptersController.#ctor(Hoclieu.Books.ChapterRepository,Hoclieu.Books.BookRepository,Hoclieu.Services.BookService,AutoMapper.IMapper,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.MongoQuestionCacheRepository)">
             <summary>
            
             </summary>
             <param name="chapterRepository"></param>
             <param name="bookRepository"></param>
             <param name="bookService"></param>
             <param name="mapper"></param>
             <param name="dbContext"></param>
             <param name="questionCacheRepository"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ChaptersController.Create(Hoclieu.Chapters.CreateChapterRequest)">
            <summary>
            Create chapter
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ChaptersController.Update(System.Guid,Hoclieu.Chapters.UpdateChapterRequest)">
            <summary>
            Cập nhật dữ liệu chương
            </summary>
            <param name="id">Định danh chương</param>
            <param name="request">Dữ liệu cập nhật</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ChaptersController.GetChapterFullOption(System.Guid)">
            <summary>
            Get chapter full option
            </summary>
            <param name="bookId"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.EBooksController">
            <summary>
            EBooksController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.BookService,Hoclieu.Books.BookRepository)">
            <summary>
            EBooksController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.GetEBookByBookId(System.Guid)">
            <summary>
            Get eBook by bookId
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.CreateEBook(Hoclieu.Core.Dtos.EBook.EBookRequest)">
            <summary>
            Create eBook
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.UpdateEBook(Hoclieu.Core.Dtos.EBook.EBookRequest)">
            <summary>
            update eBook
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.DeleteEBook(System.Int32)">
            <summary>
            delete eBook
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.GetEBookInfo(System.Int32)">
            <summary>
            Get book information
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.GetPageBySkillId(System.Guid)">
            <summary>
            Get page by skillId
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.GetPageByLinkGame(System.String)">
            <summary>
            Get page by linkGame
            </summary>        
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.GetAllPage(System.Guid)">
            <summary>
            Lấy danh sách tất cả các trang của sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.GetEBookAllPage(System.Int32)">
            <summary>
            Lấy danh sách tất cả các trang của sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.CreatePage(Hoclieu.Core.Dtos.EBook.EBookPageRequest)">
            <summary>
            Tạo một trang mới cho sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.UpdatePage(System.Guid,Hoclieu.Core.Dtos.EBook.EBookPageRequest)">
            <summary>
            Update trang sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.DeletePage(System.Guid)">
            <summary>
            Xóa trang sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.AddBox(System.Guid,Hoclieu.Core.Dtos.EBook.EBookBoxRequest)">
            <summary>
            Thêm khối nội dung cho trang sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.UpdateBox(System.Guid,System.Guid,Hoclieu.Core.Dtos.EBook.EBookBoxRequest)">
            <summary>
            Sửa khối nội dung cho trang sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.DeleteBox(System.Guid,System.Guid)">
            <summary>
            Xoá khối nội dung cho trang sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.AddCursor(System.Guid,Hoclieu.Core.Dtos.EBook.EBookCursorRequest)">
            <summary>
            Add blinking cursor of eBook page's contents
            </summary>
            <param name="eBookPageId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.UpdateCursor(System.Int32,Hoclieu.Core.Dtos.EBook.EBookCursorRequest)">
            <summary>
            Update blinking cursor of eBook page's contents
            </summary>
            <param name="cursorId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.DeleteCursor(System.Int32)">
            <summary>
            Delete blinking cursor of eBook page's contents
            </summary>
            <param name="cursorId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.UpdateSequence(System.Int32)">
            <summary>
            update pageNumber sequence by bookId
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.InsertTableOfContents(System.Int32,Hoclieu.Core.Dtos.EBook.TableOfContentsRequest)">
            <summary>
            Inserts or updates the table of contents for a specific eBook.
            </summary>
            <param name="eBookId">The ID of the eBook.</param>
            <param name="request">The request containing the table of contents data.</param>
            <returns>An IActionResult indicating the result of the operation.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.EBooksController.GetTableOfContents(System.Int32)">
            <summary>
            Get the table of contents for a specific eBook.
            </summary>
            <param name="eBookId">The ID of the eBook.</param>
            <returns>The table of contents of the eBook.</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ExtensiveResourcesController">
            <summary>
            Controller that handles the creation
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtensiveResourcesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.BookService)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtensiveResourcesController.Get(System.Nullable{System.Guid},System.Nullable{Hoclieu.Books.BookExtensiveResourceGroup})">
            <summary>
            Get Extensive Resource by book id.
            If you want to get all resources, you can't pass it.
            </summary>
            <param name="id">This param is nullable</param>
            <param name="bookExtensiveResourceGroup"></param>
            <returns>Group resource with group and book id</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtensiveResourcesController.GetGroupByChapter(System.Guid,Hoclieu.Books.BookExtensiveResourceGroup)">
            <summary>
            Get Extensive Resource by book id and group with chapter and lesson name.
            </summary>
            <param name="id">This param is nullable</param>
            <param name="bookExtensiveResourceGroup"></param>
            <returns>Group resource with group and book id</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtensiveResourcesController.GetByStudyProgrammeExtensive(System.Int32)">
            <summary>
            Get Extensive Resource by study programme book id.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtensiveResourcesController.Update(System.Guid,Hoclieu.Books.UpdateBookRequest)">
             <summary>
            
             </summary>
             <param name="bookId"></param>
             <param name="request"></param>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ExtraResourceGroupsController">
            <summary>
            Controller that handles the creation
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtraResourceGroupsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
             <summary>
            
             </summary>
             <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtraResourceGroupsController.Get">
            <summary>
            Get All Exrtra Resource Types
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtraResourceGroupsController.Get(System.Guid)">
            <summary>
            Get By Id
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtraResourceGroupsController.Post(Hoclieu.Core.Dtos.Book.ExtraResource.ExtraResourceGroupDto)">
            <summary>
            Add new resource type
            </summary>
            <param name="ext"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtraResourceGroupsController.Put(System.Guid,Hoclieu.Core.Dtos.Book.ExtraResource.ExtraResourceGroupDto)">
            <summary>
            Update resource type
            </summary>
            <returns></returns>
            <exception cref="T:System.Exception">
            Invalid id
            or
            System.Exception
            </exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExtraResourceGroupsController.Delete(System.Guid)">
            <summary>
            Delete resource type
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException">if <paramsSerializer/> is null</exception>
            <exception cref="T:System.Exception">
            Invalid id
            or
            System.Exception
            </exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.FavoriteBookController.Find(Hoclieu.Books.FindBookRequest)">
            <summary>
            Lấy sách yêu thích, mỗi lần 6 cuốn
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.LessonsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LessonsController.#ctor(Hoclieu.Books.LessonRepository,Hoclieu.Books.LessonSkillRepository,Hoclieu.Books.LessonMySkillRepository,Hoclieu.Books.ChapterRepository,Hoclieu.Users.TeacherRepository,Hoclieu.Skills.SkillTeacherRepository,Hoclieu.Books.LessonGoalRepository,Hoclieu.Services.BookService,Hoclieu.Services.SkillTeacherService,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},AutoMapper.IMapper,Hoclieu.Mongo.Service.MongoQuestionCacheRepository,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LessonsController.Create(Hoclieu.Lessons.CreateLessonRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LessonsController.FindLessonSkills(Hoclieu.Books.FindLessonSkillRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LessonsController.AddSkill(System.Guid,Hoclieu.Books.AddSectionSkillRequest)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="request"></param>
             <exception cref="T:System.ApplicationException"></exception>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController.#ctor(Hoclieu.EntityFrameworkCore.SectionGameSuggestionRepository,Hoclieu.Users.StudentRepository,Hoclieu.Services.NotificationService,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
             <summary>
            
             </summary>
             <param name="sectionGameSuggestionRepository"></param>
             <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController.CreateSectionGameSuggestion(System.Collections.Generic.List{Hoclieu.Core.Dtos.SectionGameSuggestions.SectionGameSuggestionDto})">
            <summary>
            Giao bài game
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController.Remove(System.Collections.Generic.List{System.Guid})">
            <summary>
            Xoá giao bài nhiều người
            </summary>
            <param name="requests">Danh sách định danh giao bài</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController.RemoveAllClassroom(System.Guid,System.Guid,System.Guid)">
            <summary>
            Xoá giao bài cả lớp
            </summary>
            <param name="requests">Danh sách định danh giao bài</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController.GetSectionGameSuggestionOfTeacher(System.Guid)">
             <summary>
            
             </summary>
             <param name="sectionGameId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController.GetByClassroom(System.Guid,System.Nullable{System.Guid},System.Nullable{Hoclieu.Core.Enums.Book.GameType},System.Nullable{Hoclieu.Core.Enums.Book.SectionGameSuggestionStatus},Hoclieu.Dtos.PagedAndSortedResultRequest,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32)">
             <summary>
            
             </summary>
             <param name="classroomId"></param>
             <param name="subjectId"></param>
             <param name="type"></param>
             <param name="status"></param>
             <param name="pagination"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionGameSuggestionsController.GetSectionGameSuggestionSchoolByClassroom(System.Guid,System.Guid,System.Nullable{Hoclieu.Core.Enums.Book.GameType},System.Nullable{System.Guid},Hoclieu.Dtos.PagedAndSortedResultRequest,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
             <summary>
            
             </summary>
             <param name="teacherId"></param>
             <param name="classroomId"></param>
             <param name="type"></param>
             <param name="subjectId"></param>
             <param name="pagination"></param>
             <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.SectionsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.#ctor(Hoclieu.Books.BookRepository,Hoclieu.Books.SectionRepository,Hoclieu.Skills.SkillRepository,Hoclieu.Books.SectionSkillRepository,Hoclieu.Books.SectionGameRepository,Hoclieu.Books.LessonRepository,Hoclieu.Books.GameResultRepository,Hoclieu.Users.StudentRepository,Hoclieu.EntityFrameworkCore.SectionGameSuggestionRepository,Hoclieu.Services.BookService,AutoMapper.IMapper,Hoclieu.Services.DataQuestionService,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.Worksheet.WorksheetRepository)">
             <summary>
            
             </summary>
             <param name="bookRepository"></param>
             <param name="sectionRepository"></param>
             <param name="skillRepository"></param>
             <param name="sectionSkillRepository"></param>
             <param name="sectionGameRepository"></param>
             <param name="lessonRepository"></param>
             <param name="gameResultRepository"></param>
             <param name="studentRepository"></param>
             <param name="sectionGameSuggestionRepository"></param>
             <param name="bookService"></param>
             <param name="mapper"></param>
             <param name="dataQuestionService"></param>
             <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.Create(Hoclieu.Books.CreateSectionRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Exception"></exception>
             <exception cref="T:System.Collections.Generic.KeyNotFoundException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.Update(System.Guid,Hoclieu.Books.UpdateSectionRequest)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.Delete(System.Guid)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <returns></returns>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.DeleteSkill(System.Guid,System.Guid)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="skillId"></param>
             <returns></returns>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.UpdateSkill(System.Guid,System.Guid,System.Nullable{Hoclieu.Core.Enums.Book.SectionSkillType},System.Nullable{Hoclieu.Core.Enums.Book.SectionSkillBlockOption})">
            <summary>
            cập nhật sectionskill
            </summary>
            <param name="id"></param>
            <param name="skillId"></param>
            <param name="type"></param>
            <param name="blockOption"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.UpdateGame(System.Guid,System.Nullable{Hoclieu.Core.Enums.Book.SectionGameType},System.Nullable{Hoclieu.Core.Enums.Book.SectionSkillBlockOption})">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="type"></param>
             <param name="blockOption"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.AddSkill(System.Guid,Hoclieu.Books.AddSectionSkillRequest)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.Reorder(Hoclieu.Books.ReorderSectionRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.ArgumentOutOfRangeException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.ReorderSkill(System.Guid,Hoclieu.Books.ReorderSectionSkillRequest)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.ArgumentOutOfRangeException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.ReorderGame(System.Guid,Hoclieu.Books.ReorderSectionSkillRequest)">
            <summary>
            Sắp xếp lại ví trí game trong section
            </summary>
            <param name="id"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentOutOfRangeException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.AddGame(System.Guid,Hoclieu.Books.AddSectionGameRequest)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.EditGame(System.Guid,Hoclieu.Books.AddSectionGameRequest)">
            <summary>
            Sủa game trong sách
            </summary>
            <param name="id">Định danh game</param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.GetGame(System.Guid)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.UpdateGame(System.Guid,Hoclieu.Books.AddSectionGameRequest)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.DeleteGame(System.Guid)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.SaveGameResult(System.Guid,Hoclieu.Books.SaveGameRequest)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.GetDataGameTeacherBook(System.Guid)">
            <summary>
            Lấy dữ liệu teacher book game
            </summary>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.GetChapterOption(Hoclieu.Books.GetSectionSkillsRequest)">
            <summary>
            Lấy các sectionSkill từ định danh section hoặc định danh lesson
            </summary>
            <param name="request">Dữ liệu định danh</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SectionsController.GetSkillPassNeedLogin(System.Guid)">
            <summary>
            Lấy thông tin Skill có trong bộ sách cần đăng nhập không
            </summary>
            <param name="id">Id sách</param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.CardsController">
            <summary>
            Cards Controller
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.CardService)">
            <summary>
            Cards Controller Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardsController.GetCards">
            <summary>
            Lấy danh sách card
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardsController.RakeCard(System.Guid,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.Int32})">
            <summary>
            Lấy danh sách card
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardsController.RakeCard(System.String,System.Guid,System.Int32)">
            <summary>
            Cấp kích hoạt cho user
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardsController.DeleteRakeCard(System.String,System.Guid)">
            <summary>
            Cấp CLT cho user
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.CheckpointsController">
            <summary>
            List API Liên quan đến checkpoint
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.#ctor(Hoclieu.Checkpoints.CheckpointRepository,Hoclieu.Checkpoints.CheckpointHeaderRepository,Hoclieu.Checkpoints.CheckpointDetailRepository,Hoclieu.Checkpoints.CheckpointSkillRepository,Hoclieu.Checkpoints.CheckpointTemplateRepository,Hoclieu.Skills.SkillRepository,Hoclieu.DataQuestions.SkillTemplateRepository,Hoclieu.DataQuestions.SkillTemplateDataRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheRepository,Hoclieu.Checkpoints.CheckpointResultRepository,Hoclieu.Books.BookRepository,Hoclieu.Users.StudentRepository,Hoclieu.Users.TeacherRepository,Hoclieu.Schools.SchoolStudentRepository,Hoclieu.EntityFrameworkCore.Books.BookCheckpointCacheRepository,Hoclieu.EntityFrameworkCore.Skills.SkillCheckpointCacheRepository,Hoclieu.Skills.SkillTeacherSharesRepository,Hoclieu.Services.DataQuestionService,Hoclieu.Services.CheckpointService,Hoclieu.Services.ClassroomService,Hoclieu.Services.SchoolManagerService,Hoclieu.Services.DepartmentManagerService,Hoclieu.Services.BookService,Hoclieu.Services.EmailService,Hoclieu.Services.TestBank.TestService,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.Extensions.Caching.Distributed.IDistributedCache,AutoMapper.IMapper,Hoclieu.Services.SkillService,Hoclieu.Mongo.Service.MongoQuestionRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheInfoRepository,Hoclieu.Mongo.Service.MongoGroupContentRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheKnowledgeRepository,Hoclieu.Mongo.Service.MongoCheckpointKnowledgeRepository)">
            <summary>
            Hàm khởi tạo
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateSkillCheckpoint(System.Guid,Hoclieu.Checkpoints.UpdateSkillCheckpoint)">
             <summary>
            
             </summary>
             <param name="skillId"></param>
             <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CreateHeaderCheckpoint(Hoclieu.Checkpoints.CreateOrEditCheckpointHeaderRequest)">
            <summary>
            CreateHeaderCheckpoint
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateHeaderCheckpoint(System.Guid,Hoclieu.Checkpoints.CreateOrEditCheckpointHeaderRequest)">
             <summary>
            
             </summary>
             <param name="checkPointHeaderId"></param>
             <param name="request"></param>
             <exception cref="T:System.NullReferenceException"></exception>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.DeleteHeaderCheckpoint(System.Guid)">
            <summary>
            DeleteHeaderCheckpoint
            </summary>
            <param name="checkPointHeaderId"></param>
            <exception cref="T:System.NullReferenceException"></exception>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CreateCheckpointDetail(Hoclieu.Checkpoints.CreateOrEditCheckpointDetailRequest)">
            <summary>
            CreateCheckpointDetail
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.NullReferenceException"></exception>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateCheckpointDetail(System.Guid,Hoclieu.Checkpoints.CreateOrEditCheckpointDetailRequest)">
            <summary>
            UpdateCheckpointDetail
            </summary>
            <param name="checkPointDetailId"></param>
            <param name="request"></param>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.DeleteCheckpointDetail(System.Guid)">
            <summary>
            DeleteCheckpointDetail
            </summary>
            <param name="checkPointDetailId"></param>
            <exception cref="T:System.NullReferenceException"></exception>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CreateCheckpointSkill(Hoclieu.Checkpoints.CreateOrEditCheckpointSkillRequest)">
            <summary>
            CreateCheckpointSkill
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateCheckpointSkill(System.Guid,Hoclieu.Checkpoints.UpdateCheckpointSkillRequest)">
            <summary>
            UpdateCheckpointSkill
            </summary>
            <param name="checkPointSkillId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.DeleteCheckpointSkill(System.Guid)">
            <summary>
            DeleteCheckpointSkill
            </summary>
            <param name="checkPointSkillId"></param>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CreateCheckpointSkillTemplate(Hoclieu.Checkpoints.CreateOrEditCheckpointSkillTemplateRequest)">
            <summary>
            CreateCheckpointSkillTemplate
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.NullReferenceException"></exception>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.DeleteCheckpointTemplate(System.Guid)">
            <summary>
            DeleteCheckpointTemplate
            </summary>
            <param name="checkPointTemplateId"></param>
            <exception cref="T:System.NullReferenceException"></exception>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CopyCheckpointDetail(System.Guid)">
            <summary>
            API copy cụm checkpoint
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetQuestionCheckpointCache(System.Guid)">
            <summary>
            API lấy nội dung câu hỏi từ checkpointQuestionCacheId
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetQuestionCheckpoint(System.Guid,System.Nullable{System.Int32},Hoclieu.Core.Enums.ClientType)">
            <summary>
            API tạo đề của kỹ năng checkpoint
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.SubmissionAnswer(Hoclieu.Checkpoints.CheckpointSubmissionAnswerRequest)">
            <summary>
            CheckpointSubmit
            </summary>
            <param name="request"></param>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.SubmissionCheckpoint(System.Guid)">
             <summary>
            
             </summary>
             <param name="checkpointCacheId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.DeleteCheckpointCache(System.Guid)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.StartCheckpoint(System.Guid)">
             <summary>
            
             </summary>
             <param name="checkpointCacheId"></param>
             <returns></returns>
             <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpoints(System.Guid,System.Guid)">
            <summary>
            Get checkpoint result
            </summary>
            <param name="gradeId"></param>
            <param name="subjectId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointEssays(System.Guid)">
             <summary>
            
             </summary>
             <param name="skillId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointAnalytic(System.Guid,Hoclieu.Core.Dtos.CheckpointAnalyticQuery)">
            <summary>
            API thống kê điểm luyện tập trung bình
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpoint(System.Guid)">
             <summary>
            
             </summary>
             <param name="checkpointId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointSkillTrouble(System.Guid,System.Nullable{System.Guid},System.Nullable{System.Guid},Hoclieu.Core.Dtos.CheckpointAnalyticQuery)">
            <summary>
            API lấy ra các kỹ năng hay sai khi làm đề
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointRanking(System.Guid,Hoclieu.Checkpoints.CheckpointRankingRequest)">
            <summary>
            API lấy ra các câu hỏi hay sai khi làm đề
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointResultRecent(System.Guid,System.Int32,System.Int32,Hoclieu.Checkpoints.FilterTime)">
            <summary>
            Get checkpoint result recent
            </summary>
            <param name="checkpointId"></param>
            <param name="skipCount"></param>
            <param name="maxResultCount"></param>
            <param name="filterTime"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointResultDetail(System.Guid)">
            <summary>
            Lấy thông tin lịch sử làm bài checkpoint
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointSampleDetail(System.Guid)">
            <summary>
            API lấy thông tin đề thừ checkpointQuestionCache
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointSampleDetailPublic(System.Guid)">
            <summary>
            API lấy thông tin đề thừ checkpointQuestionCache
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointRanking(System.Guid,Hoclieu.Checkpoints.CheckpointRankingTeacherRequest)">
            <summary>
            Get checkpoint ranking
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointRankingDepartment(System.Guid,Hoclieu.Checkpoints.CheckpointRankingSchoolRequest)">
            <summary>
            Lấy xếp hạng luyện tập học sinh trong sở
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointResultRecentByStudentId(System.Guid,System.Guid,System.Int32,System.Int32,Hoclieu.Checkpoints.FilterTime)">
            <summary>
            Get checkpoint practice
            </summary>
            <param name="checkpointId"></param>
            <param name="studentId"></param>
            <param name="skipCount"></param>
            <param name="maxResultCount"></param>
            <param name="filterTime"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointAnalyticByStudent(System.Guid,System.Guid,Hoclieu.Core.Dtos.CheckpointAnalyticQuery)">
            <summary>
            Get checkpoint analytic by student
            </summary>
            <param name="checkpointId"></param>
            <param name="studentId"></param>
            <param name="query"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointRankingSchool(System.Guid,Hoclieu.Checkpoints.CheckpointRankingTeacherRequest)">
            <summary>
            Get checkpoint ranking
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointPracticeSchool(System.Guid,Hoclieu.Checkpoints.CheckpointRankingTeacherRequest,System.Int32,System.Int32,System.Int32)">
            <summary>
            Get checkpoint practice
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <param name="timeZone"></param>
            <param name="skipCount"></param>
            <param name="maxResult"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointPracticeDepartment(System.Guid,Hoclieu.Checkpoints.CheckpointRankingSchoolRequest,System.Int32,System.Int32,System.Int32)">
            <summary>
            Lấy thông tin luyện tập của học sinh theo trường trong sở
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <param name="timeZone"></param>
            <param name="skipCount"></param>
            <param name="maxResult"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CountDoCheckpoint(System.Guid)">
             <summary>
            
             </summary>
             <param name="skillId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateBookSampleCheckpoint(System.Guid,System.Int32,System.Guid)">
            <summary>
            gán sách
            </summary>
            <param name="bookId"></param>
            <param name="checkpointSampleIndex"></param>
            <param name="checkpointCacheId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetBookCheckpointCache(System.Guid)">
            <summary>
            API lấy ra các đề mẫu của sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckPointQuestionLevel(System.Collections.Generic.List{System.Guid})">
            <summary>
            Lấy mức độ câu hỏi trong checkpoint
            </summary>
            <param name="skillTemplateDataIds">Danh sách định danh skilltemplatedata</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckPointDetailQuestionData(System.Guid)">
            <summary>
            Get question data for checkpoint detail
            </summary>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateCheckpointHeader(System.Guid,System.Guid,Hoclieu.Checkpoints.DoingCheckpointDto)">
            <summary>
            Cập nhật header đề mẫu
            </summary>
            <param name="checkpointCacheId">Định danh đề mẫu</param>
            <param name="checkpointHeaderId">Định danh header</param>
            <param name="doingCheckpoint">Dữ liệu câu hỏi mới</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateCheckpointHeaderTestBank(System.Guid,System.Guid,Hoclieu.Checkpoints.DoingCheckpointDto)">
            <summary>
            Cập nhật header đề mẫu testBank
            </summary>
            <param name="checkpointCacheId">Định danh đề mẫu</param>
            <param name="checkpointHeaderId">Định danh header</param>
            <param name="doingCheckpoint">Dữ liệu câu hỏi mới</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetQuestionByCheckpointQuestionCacheId(System.Guid,Hoclieu.Checkpoints.GetCheckpointQuestionRequests)">
            <summary>
            Tìm kiếm câu hỏi để gán vào đề mẫu
            </summary>
            <param name="checkpointQuestionCacheId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateCheckpointQuestionCache(System.Guid,Hoclieu.Checkpoints.DoingCheckpointQuestionDto)">
            <summary>
            Cập nhật câu hỏi trong đề mẫu
            </summary>
            <param name="checkpointQuestionCacheId"></param>
            <param name="doingCheckpointQuestion"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.UpdateCheckpointDetailTestBank(System.Guid,Hoclieu.Checkpoints.UpdateCheckpointDetailRequest)">
            <summary>
            Cập nhật detail đề mẫu testBank
            </summary>
            <param name="checkpointCacheId">Định danh đề mẫu</param>
            <param name="doingCheckpoint">Dữ liệu câu hỏi mới</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.LookCheckpoint(System.Guid,Hoclieu.Core.Enums.LockEditCheckpointStatus)">
            <summary>
            Khóa chỉnh sửa đề kiểm tra
            </summary>
            <param name="checkpointId"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.LookCheckpointCache(System.Guid,Hoclieu.Core.Enums.LockEditCheckpointCacheStatus)">
            <summary>
            Khóa chỉnh sửa đề mẫu
            </summary>
            <param name="checkpointCacheId"></param>
            <param name="status"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.MakeDraftCheckpointCache(System.Guid)">
            <summary>
            Tạo bản nháp đề tĩnh
            </summary>
            <param name="checkpointCacheId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CreateDraftForCelm(System.Guid)">
            <summary>
            Tạo bản nháp cho đề tĩnh người dùng đã lưu
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CheckSameName(System.Guid,System.String)">
            <summary>
            Check name skill exist
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.SaveCheckpointCacheForCalmTest(System.Guid,Hoclieu.Core.Dtos.Checkpoint.CheckpointDarftRequest)">
            <summary>
            Save checkpoint cache for calm test
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CloneCheckpointCacheForCalmTest(System.Guid)">
            <summary>
            copy checkpoint cache for calm test
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.SaveCheckpointCache(System.Guid,System.String,System.Nullable{System.Int32},System.Nullable{Hoclieu.Core.Enums.SaveCheckpointCacheOption},Hoclieu.Core.Enums.SkillCheckpointCacheType)">
            <summary>
            Save checkpoint cache for calm test
            </summary>
            <param name="checkpointCacheId"></param>
            <param name="name"></param>
            <param name="time"></param>
            <param name="option"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.SaveTimeForCustomTestBank(System.Guid,System.Int32)">
            <summary>
            Save time for custom test bank
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.CountTeacherCheckpointCache(System.Guid)">
             <summary>
            
             </summary>
             <param name="checkpointId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetSkillTeacherCheckpointCacheByCheckpoint(System.Guid)">
             <summary>
            
             </summary>
             <param name="checkpointId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CheckpointsController.GetCheckpointNeedLogin(System.Guid)">
            <summary>
            Lấy thông tin checkpoint có cần đăng nhập không
            </summary>
            <param name="id">Id sách</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomNewsfeedController.GetRecentPosts">
            <summary>
            Lấy 5 bài đăng gần đây từ các lớp người dùng đang tham gia (trong vòng 7 ngày)
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ClassroomsController">
            <summary>
            Classroom API
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Classrooms.ClassroomRepository,Hoclieu.Classrooms.ClassroomTeacherRepository,Hoclieu.Classrooms.ClassroomStudentRepository,Hoclieu.Users.TeacherRepository,Hoclieu.Users.StudentRepository,Hoclieu.Users.ParentRepository,Hoclieu.Schools.SchoolStudentRepository,Hoclieu.Schools.SchoolTeacherRepository,Hoclieu.SkillSuggestions.SkillSuggestionRepository,Hoclieu.Notifications.NotificationRepository,Hoclieu.Services.ClassroomService,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Hoclieu.Services.UserService,Hoclieu.Classrooms.JoinClassroomInvitationRepository,Hoclieu.Users.SchoolManagerRepository,Hoclieu.Services.NotificationService,AutoMapper.IMapper)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetClassrooms(System.Nullable{System.Guid},System.Nullable{System.Guid},System.Nullable{Hoclieu.Core.Enums.ClassroomStatus},System.Nullable{System.Boolean})">
            <summary>
            Get all classrooms
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetClassroomsNoMap(System.Nullable{System.Guid},System.Nullable{System.Guid},System.Nullable{Hoclieu.Core.Enums.ClassroomStatus})">
            <summary>
            Get class not use map
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetNewClassrooms(System.DateTime,System.Int32)">
            <summary>
            Get new classrooms
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetStatisticClassrooms(System.DateTime,System.Nullable{System.DateTime},System.Int32,Hoclieu.Core.Enums.ClassroomStatus)">
            <summary>
            Get statistic for classroom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetClassroomsBySchoolId(System.Guid)">
            <summary>
            get classroom by school-id
            </summary>
            <param name="schoolId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.CreateRangeClassroom(System.Collections.Generic.List{Hoclieu.Classrooms.CreateClassroomRequest})">
            <summary>
            Tao danh sach lop hoc
            </summary>
            <param name="requests"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetStudentsBookAsync(System.Guid)">
            <summary>
            Lấy thông tin sách luyện thi của từng học sinh trong lớp
            </summary>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.TransferOwner(Hoclieu.Classrooms.OwnerOfClassroom,System.Guid)">
            <summary>
            change role teacher to owner
            then change role owner to teacher
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.UpgradeClassroom(System.Guid,Hoclieu.Classrooms.UpdateClassroomRequest)">
            <summary>
            API lên lớp
            </summary>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.ArchiveClassroom(System.Guid,Hoclieu.Core.Enums.ClassroomStatus)">
            <summary>
            Đưa lớp vào lưu trữ
            </summary>
            <param name="classroomId"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.LeaveClassroom(System.Guid)">
            <summary>
            Rời lớp
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetCurrentSchoolYear">
            <summary>
            GetCurrentSchoolYear
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.GetClassesToEndOfTeacher">
            <summary>
            Get classes to end of teacher
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ClassroomsController.UpdateListClass(System.Collections.Generic.List{Hoclieu.Classrooms.ClassroomDto})">
            <summary>
            Update classroom status
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.CompetitionsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.AspNetCore.SignalR.IHubContext{Hoclieu.Hubs.ContestHub},Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings},Hoclieu.Mongo.Service.ContestResultQuestionRepository,Hoclieu.Mongo.Service.ContestCacheRepository,Hoclieu.Services.CuocThi.ContestService,Hoclieu.Mongo.Service.ContestResultRepository,Hoclieu.Mongo.Service.MongoQuestionRepository)">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.GetCompetitionExamDto(System.String)">
            <summary>
            Lấy thông tin phòng thi dựa trên mã
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.GetCandidateInfo(System.String,System.String)">
            <summary>
            Lấy thông tin học sinh qua SBD
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.StartContest(System.String,System.String)">
            <summary>
            Đăng nhập vào phòng thi
            </summary>
            <param name="candidateCode"></param>
            <param name="competitionExamCode"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.GetContestInfo(Hoclieu.Core.Dtos.CuocThi.GetContestExamInfoRequest)">
            <summary>
            Lấy thông tin bài làm
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.StartExam(Hoclieu.Core.Dtos.CuocThi.StartExamRequest)">
            <summary>
            Bắt đầu làm bài
            </summary>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.SubmissionContestAnswer(Hoclieu.Core.Dtos.CuocThi.ContestSubmissionAnswerRequest)">
            <summary>
            Answer a question in contest
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.SubmitContestAnswer(Hoclieu.Core.Dtos.CuocThi.SubmitContestAnswerRequest)">
            <summary>
            Submit contest
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.DeleteContestAnswer(System.Guid)">
             <summary>
            
             </summary>
             <param name="contestResultId"></param>
             <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CompetitionsController.GetResultInfo(System.String,System.String)">
            <summary>
            Get contest info for result
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController">
            <summary>
            API xử lý sự cố trong bài thi
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.#ctor(Hoclieu.Services.Competition.TroubleShootingService,Hoclieu.Mongo.Service.ContestResultRepository,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
             <summary>
            
             </summary>
             <param name="context"></param>
             <param name="troubleShooting"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.StopAllCompetitionExam(Hoclieu.Core.Dtos.Competition.StopContestRequest)">
            <summary>
            Dừng bài thi của thí sinh
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.StopAllCompetitionExam(System.String)">
             <summary>
            
             </summary>
             <param name="competitionExamCode"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.ContinueCompetitionExam(Hoclieu.Core.Dtos.Competition.StopContestRequest)">
            <summary>
            Tiếp tục bài thi cho thí sinh
            </summary>
            <param name="competitionExamCode"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.AddTimeCandidateCompetitionExam(Hoclieu.Core.Dtos.Competition.AddTimeContestRequest)">
            <summary>
            Thêm thời gian cho thí sinh
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.GetDoingCandidate(System.Int32)">
            <summary>
            Lấy danh sách thí sinh đang làm bài thi
            </summary>
            <param name="vrCompetitionExamId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.GetPostponedCandidate(System.Int32)">
             <summary>
            
             </summary>
             <param name="vrCompetitionExamId"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.GetOngoingContest(System.Int32,System.Int32,System.String)">
             <summary>
            
             </summary>
             <param name="skipCount"></param>
             <param name="maxResultCount"></param>
             <param name="codeSearch"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.GetOngoingContestCandidates(System.String,System.Int32,System.Int32)">
             <summary>
            
             </summary>
             <param name="competitionExamCode"></param>
             <param name="skipCount"></param>
             <param name="maxResultCount"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Competition.TroubleShootingController.GetManageDashboard(System.Int32)">
             <summary>
            
             </summary>
             <param name="vrCompetitionId"></param>
             <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ConfigController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ConfigController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Constructor
            </summary>
            <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ConfigController.Get">
            <summary>
            API get config
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ConfigController.CreateOrUpdate(Hoclieu.Configs.CreateConfigRequest)">
            <summary>
            API update create
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CuocThi.ContestCachesController.DeleteContestCache(System.Guid)">
            <summary>
            Xoá đề thi
            </summary>
            <param name="contestCacheId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CuocThi.ContestCachesController.GetCompetitionResultById(System.Int32)">
            <summary>
            Xem danh kết quả của phòng thi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CuocThi.ContestCachesController.GetExamDetailStatistics(System.Int32)">
            <summary>
            Thống kê SL đúng, sai, chưa làm theo từng question
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.CuocThi.ContestCacheTestController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CuocThi.ContestCacheTestController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.ContestCacheRepository,Hoclieu.Mongo.Service.MongoQuestionRepository,Hoclieu.Mongo.Service.MongoGroupContentRepository,Hoclieu.Mongo.Service.MongoCheckpointKnowledgeRepository,Hoclieu.Mongo.Service.ContestResultRepository)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CuocThi.ContestCacheTestController.GetContestCacheQuestions(System.Guid,System.Nullable{System.Guid})">
            <summary>
            Lấy thông tin chi tiết đề thi
            </summary>
            <param name="contestCacheId"></param>
            <param name="contestResultId"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CuocThi.ContestCacheTestController.GetContestCacheBySkill(System.Guid)">
            <summary>
            Lấy danh sách đề thi đã gán của đề
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.DesktopAppVersionController.GetDesktopAppVersion">
             <summary>
            
             </summary>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.DesktopAppVersionController.UpdateDesktopAppVersion(Hoclieu.Core.Dtos.DesktopAppVersionRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.DomainConfigController">
            <summary>
            DomainConfigController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.DomainConfigController.#ctor(Hoclieu.Services.DomainConfigService)">
            <summary>
            Constructor
            </summary>
            <param name="domainConfigService"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.DomainConfigController.GetAllDomainConfig">
            <summary>
            Get all domain config
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.DomainConfigController.CreateDomainConfig(Hoclieu.Domain.Config.DomainConfigRequest)">
            <summary>
            create domain config
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.DomainConfigController.DeleteDomainConfig(System.Int32)">
            <summary>
            Delete domain config
            </summary>
            <param name="domainConfigId"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.DomainConfigController.UpdateDomainConfig(System.Int32,Hoclieu.Domain.Config.DomainConfigRequest)">
            <summary>
            update domain config
            </summary>
            <param name="domainConfigId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ErrorsController">
            <summary>
            Controller
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ErrorsController.#ctor(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ErrorsController.PostError(Hoclieu.Dtos.PostErrorRequest)">
            <summary>
            API create error
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.LabelService)">
            <summary>
            constructor
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateOrUpdateLessonUnit(Hoclieu.Core.Dtos.ExamMatrix.LessonUnitDto)">
            <summary>
            Create lesson unit
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateListLessonUnit(System.Collections.Generic.List{Hoclieu.Core.Dtos.ExamMatrix.LessonUnitDto})">
            <summary>
            Create list lesson unit
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetLessonUnit(Hoclieu.Core.Dtos.ExamMatrix.LessonUnitRequest)">
            <summary>
            Get lesson unit
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetLessonUnitById(System.Int32)">
            <summary>
            Get lesson unit by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.RemoveLessonUnit(System.Int32)">
            <summary>
            remove lesson unit by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CheckLessonUnitExistInKnowledgeSkillValueLabel(System.Int32)">
            <summary>
            check lesson unit exist in table knowledge skill value label
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetLessonUnitBySubjectAndGrade(Hoclieu.Core.Dtos.ExamMatrix.LesssonRequest)" -->
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateOrUpdateKnowledgeSkill(Hoclieu.Core.Dtos.ExamMatrix.KnowledgeSkillDto)">
            <summary>
            create knowledge skill
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateListKnowledgeSkill(System.Collections.Generic.List{Hoclieu.Core.Dtos.ExamMatrix.KnowledgeSkillDto})">
            <summary>
            Create list lesson unit
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetListKnowledgeSkill(Hoclieu.Core.Dtos.ExamMatrix.KnowledgeSkillRequest)">
            <summary>
            Get list knowledge skill
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetKnowledgeSKillById(System.Int32)">
            <summary>
            Get knowledge skill by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CheckListKnowledgeSkillDependency(System.Int32)">
            <summary>
            Check danh sách các nhãn phụ thuộc
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.RemoveKnowledgeSkill(System.Int32)">
            <summary>
            Remove knowledge skill by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetKnowledgeSkillBySubject(System.Guid)">
            <summary>
            Get knowledge skill by subjectId
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetKnowledgeForTestbank(Hoclieu.Core.Dtos.ExamMatrix.KnowledgeSkillForTestBankRequest)">
            <summary>
            Get Label and value belong knowledge skill
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateLabel(Hoclieu.Core.Dtos.ExamMatrix.LabelDto)">
            <summary>
             Create label
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.UpdateLabel(System.Int32,Hoclieu.Core.Dtos.ExamMatrix.LabelDto)">
            <summary>
            update label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.UpdateValueLabel(System.Int32,Hoclieu.Core.Dtos.ExamMatrix.ValueLabelDto)">
            <summary>
            update value label by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetLabelById(System.Int32)">
            <summary>
            get label by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.PaginateLabel(Hoclieu.Core.Dtos.ExamMatrix.LabelRequest)">
            <summary>
            Paginate label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CheckLabelExistInKnowledgeSkillValueLabel(System.Int32)">
            <summary>
            check label exist in knowledge skill value label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.RemoveLabel(System.Int32)">
            <summary>
            remove value label by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CheckValueLabelExistInKnowledgeSkillValueLabel(System.Int32)">
            <summary>
            check value label exist in knowledge skill value label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CanDeleteAllLabels">
            <summary>
            check can delete all labels
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.RemoveValueLabel(System.Int32)">
            <summary>
            remove value label by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.RemoveAllLabel">
            <summary>
            remove all labels
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateListLabel(System.Collections.Generic.List{Hoclieu.Core.Dtos.ExamMatrix.LabelDto})">
            <summary>
            create list label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetLabelAndValueLabelByKnowledgeSkillId(System.Collections.Generic.List{System.Int32})">
            <summary>
            Get label and value label by knowledge skill id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetLabelAndValueLabelByKnowledgeSkillAndLessonUnit(Hoclieu.Core.Dtos.ExamMatrix.LabelByKnowledgeSkillRequest)">
            <summary>
            Lấy danh sách nhãn và giá trị của nhãn theo knowledge skill and lesson unit
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetLabelNotDependencyByKnowledgeSkill(System.Int32,System.String)">
            <summary>
            Lấy nhan không phụ thuộc theo knowledge skill
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateKnowledgeValueLabel(Hoclieu.Core.Dtos.ExamMatrix.KnowledgeValueLabelDto)">
            <summary>
            create knowledge value label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateListKnowledgeValueLabel(System.Collections.Generic.List{Hoclieu.Core.Dtos.ExamMatrix.KnowledgeValueLabelDto})">
            <summary>
            create list knowledge value label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.RemoveKnowledgeValueLabel(Hoclieu.Core.Dtos.ExamMatrix.KnowledgeSkillValueLabelDto)">
            <summary>
            remove knowledge value label by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CheckLabelExistInKnowledgeValueLabel(System.Int32)">
            <summary>
            check label exist in knowledge value label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetKnowledgeValueLabelAllBySubject(System.Nullable{System.Guid},System.String)">
            <summary>
            Get knowledge value label all
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.DeleteKnowledgeValueLabel">
            <summary>
            Xoá toàn bộ KnowledgeValueLabel
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.SearchValueLabel(System.Int32,Hoclieu.Core.Dtos.ExamMatrix.SearchLabelRequest)">
            <summary>
            search value label
            <route>search-value-babel/{id}</route>
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateKnowledgeSkillLabel(Hoclieu.Core.Dtos.ExamMatrix.CreateKnowledgeSkillLabelDto)">
            <summary>
            create knowledge skill label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.CreateListKnowledgeSkillLabel(System.Collections.Generic.List{Hoclieu.Core.Dtos.ExamMatrix.KnowledgeSkillLabelDto})">
            <summary>
            Create list knowledge skill label
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.RemoveKnowledgeSkillLabel(System.Int32)">
            <summary>
             remove knowledge skill label by id
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamMatrix.LabelController.GetKnowledgeValueLabelAll(System.Nullable{System.Guid})">
            <summary>
            Get knowledge label all
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Facilities.AssetController.GetAllByRoomIdAsync(System.Int32)">
            <summary>
            Get all by room id
            </summary>
            <param name="roomId"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.GlobalSpeak.GlobalSpeakController">
            <summary>
            Controller cho Global Speak.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlobalSpeak.GlobalSpeakController.#ctor(Hoclieu.Services.GlobalSpeakServices)">
            <summary>
            Controller cho Global Speak.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlobalSpeak.GlobalSpeakController.FetchAllGrades">
            <summary>
            Lấy dữ liệu sách Global Speak. 
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlobalSpeak.GlobalSpeakController.UpdateGrade(System.Guid)">
            <summary>
            Cập nhật sách theo id.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.HomeController">
            <summary>
            HomeController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.#ctor(Hoclieu.Categories.CategoryRepository,Hoclieu.Skills.SkillRepository,Hoclieu.DescriptionFolders.DescriptionFolderRepository,Hoclieu.Subjects.SubjectRepository,Hoclieu.Grades.GradeRepository,Hoclieu.GradeSubjects.GradeSubjectRepository,Hoclieu.Banners.BannerRepository,Hoclieu.Notifications.NotificationRepository,Hoclieu.Users.UserSettingRepository,Hoclieu.Users.LanguageRepository,Hoclieu.Users.StudentRepository,Hoclieu.Services.CategoryService,Hoclieu.EmailCheckers.EmailCheckerService,Hoclieu.Services.EmailService,Hoclieu.Services.NotificationService,Hoclieu.Services.UserService,Hoclieu.Services.OAZalo.OAZaloService,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Microsoft.AspNetCore.Identity.RoleManager{Hoclieu.Domain.User.ApplicationRole},Hoclieu.Services.ClassroomService,Microsoft.Extensions.Caching.Distributed.IDistributedCache,Hoclieu.EntityFrameworkCore.HoclieuDbContext,AutoMapper.IMapper,Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings},Hoclieu.Services.SmsService,Hoclieu.Services.AccountVerificationService,Hoclieu.Services.User.TenancyService,Hoclieu.Services.User.TenancyUserManager)">
            <summary>
            Hàm khởi tạo controller home
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.GetVersion">
            <summary>
            Get version
            </summary>
            <returns>Version</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.Get(Hoclieu.Core.Enums.User.Languages,System.String)">
            <summary>
            Lấy thông tin khởi tạo hê thống
            </summary>
            <param name = "type" >Loại ngôn ngữ</param>
            <returns>Thông tin khởi tạo hê thống</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.GetAllGrade">
            <summary>
            Get grade
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.GetTime">
            <summary>
            get current time in server
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.RakeFolderGrade(System.String,System.Guid)">
            <summary>
            tạo folder cho khối
            </summary>
            <param name="rootId"></param>
            <param name="gradeId"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.RakeFolderSubject(System.String,System.Guid)">
            <summary>
            tạo folder cho môn học
            </summary>
            <param name="rootId"></param>
            <param name="subjectId"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.GetMyLanguage">
            <summary>
            Lấy ngôn ngữ người dùng
            </summary>
            <returns> Ngôn ngữ người dùng</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.GetCurentTime">
            <summary>
            Lấy thời gian hiện thời
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.TransferDrive(System.Int32)">
            <summary>
            sao chép drive
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.RakeEBook">
            <summary>
            Rake EBook
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.CopyEBook(System.Guid,System.Guid)">
            <summary>
            Copy EBook
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.Chk">
            <summary>
            To check if backend down or not
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.HomeController.AuthUser">
            <summary>
            To check auth
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ImageController">
            <summary>
            Quản lý ảnh
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.#ctor(Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings},Hoclieu.Services.FileService)">
            <summary>
            Hàm khởi tạo controller image
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.Avatar(System.String,System.Int32,System.Int32)">
            <summary>
            Lấy dữ liệu ảnh đại diện dựa vào tên người dùng
            </summary>
            <param name = "id" >Id người dùng</param>
            <param name = "width" >Chiều ngang</param>
            <param name = "height" >Chiều dọc</param>
            <returns>Dữ liệu ảnh đại diện</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadAvatar(Microsoft.AspNetCore.Http.IFormFile)">
             <summary>
            
             </summary>
             <param name="file"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.RemoveAvatar">
             <summary>
            
             </summary>
             <param name="file"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadBook(System.Guid,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Tải ảnh bìa sách
            </summary>
            <param name = "id" >Id sách</param>
            <param name = "file" >Dữ liệu ảnh</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadClassroomCoverImage(System.Guid,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Đăng ảnh bìa lớp học
            </summary>
            <param name="id">Định danh lớp học</param>
            <param name="image">Dữ liệu hình ảnh</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadClassroomAvatarImage(System.Guid,Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Đăng ảnh đại diện lớp học
            </summary>
            <param name="id">Định danh lớp học</param>
            <param name="image">Dữ liệu hình ảnh</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageQuestion(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Tải ảnh câu hỏi
            </summary>
            <param name = "image" >Dữ liệu ảnh</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadQuestionImage(Microsoft.AspNetCore.Http.IFormFile,System.Boolean)">
            <summary>
            Tải ảnh lên thư mục questions
            </summary>
            <param name="image">Dữ liệu ảnh</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadMarkQuestionImage(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Tải ảnh comment chấm bài LMS
            </summary>
            <param name="image">Dữ liệu ảnh comment chấm bài LMS</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadTeacherVerificationImage(System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
             <summary>
            
             </summary>
             <param name="images"></param>
             <returns></returns>
             <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadKnowledgeMatrixImage(Microsoft.AspNetCore.Http.IFormFile,System.String)">
            <summary>
            Tải ảnh lên thư mục knowledge-matrix/image (chỉ sử dụng để upload ảnh ở ma trận kiến thức)
            </summary>
            <param name="image">Dữ liệu ảnh</param>
            <param name="pathOld">Link ảnh cũ</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadMp3(Hoclieu.Core.Dtos.Audio.UpLoadAudioCloudRequest)">
            <summary>
            Tải file dạng bài tự luận
            </summary>
            <param name="dataAudio">Dữ liệu tệp</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageAgentPayment(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Tải ảnh thông tin thanh toán
            </summary>
            <param name="image">Dữ liệu ảnh</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageGlossary(System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            Tải ảnh glossary
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageStudyProgramme(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            upload image for study programme
            </summary>
            <param name="image"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageTestBankFeedback(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            upload image for testbank feedback
            </summary>
            <param name="image"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageLandingPageNew(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Up ảnh xem trước cho Landing Page News
            </summary>
            <param name="image">Dữ liệu ảnh</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException">Ngoại lệ hệ thống</exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageClassroomNewfee(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Tải ảnh bài đăng lớp học
            /// </summary>
            <param name="image">Dữ liệu ảnh</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageCommentReviewSkill(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Tải hình ảnh comment review skill
            </summary>
            <param name="image"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.GetImageTest(Hoclieu.Core.Dtos.ImageResizeDto)">
            <summary>
            API thay đổi kích thước của hình ảnh lấy từ URL
            </summary>
            <param name = "url" >Địa chỉ url của ảnh (hỗ trợ ảnh JPG, PNG, ?)</param>
            <returns>Ảnh đã được điều chỉnh kích thước</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadDestopApp(System.Guid,System.Int32,System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile},Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
             Tải ảnh lên thư mục questions
            </summary>
            <param name="bookId"></param>
            <param name="version"></param>
            <param name="questionData"></param>
            <param name="book"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadMp3ForTeacher(Hoclieu.Core.Dtos.Audio.UpLoadAudioCloudRequest)">
            <summary>
            Tải file dạng bài tự luận cho giáo viên
            </summary>
            <param name="dataAudio">Dữ liệu tệp</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadImageMyQuestionAi(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Tải ảnh tạo câu hỏi từ AI
            </summary>
            <param name="image"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadMp3ForAi(Hoclieu.Core.Dtos.Audio.UpLoadAudioCloudRequest)">
            <summary>
            Tải audio tạo câu hỏi từ AI
            </summary>
            <param name="dataAudio"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ImageController.UploadFileAi(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            Upload file tạo câu hỏi từ AI
            </summary>
            <param name="file"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LanguageController.Searchs(System.Int32,System.Int32,System.String,System.String,System.Nullable{Hoclieu.Core.Enums.User.Languages})">
            <summary>
            tìm kiếm, lọc ngôn ngữ đê quản trị
            </summary>
            <param name = "request" > Dữ liệu bộ lọc</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LanguageController.adds(System.Collections.Generic.List{Hoclieu.Core.Dtos.User.LanguageDto})">
            <summary>
            Thêm ngôn ngữ dịch
            </summary>
            <param name="request">Dữ liệu ngôn ngữ dịch</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LanguageController.Put(System.Guid,Hoclieu.Core.Dtos.User.LanguageJsonDto)">
            <summary>
            Cập nhật dữ liệu ngôn ngữ dịch
            </summary>
            <param name="id"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LanguageController.Delete(System.Guid)">
            <summary>
            Xóa ngôn ngữ dịch
            </summary>
            <param name="id">Định danh ngôn ngữ</param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.LearningJourney.LearningJourneyController">
            <summary>
            Controller hành trình học tập của học sinh.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LearningJourney.LearningJourneyController.#ctor(Hoclieu.Users.StudentRepository,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.MongoSuggestion.MongoSuggestionStudentDataRepository,Hoclieu.Mongo.Service.MongoSuggestion.MongoSuggestionRepository,Hoclieu.Mongo.Service.StudyTrackingRepository,Hoclieu.Mongo.Service.MongoSkillResultRepository,Hoclieu.Classrooms.ClassroomStudentRepository,Hoclieu.Mongo.Service.StudyLogsRepository,Hoclieu.Services.User.TenancyUserManager)">
            <summary>
            Controller hành trình học tập của học sinh.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LearningJourney.LearningJourneyController.CheckStudyLog">
            <summary>
            Kiểm tra trạng thái học tập của người dùng cho ngày hôm nay
            </summary>
            <returns>
            Trả về thông tin:
            - HasStudiedBefore: True nếu người dùng đã từng học trước đây
            - TodayTimeStudy: Tổng thời gian học trong ngày hôm nay (ms)
            </returns>
            <response code="200">Trả về thông tin trạng thái học tập thành công</response>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LearningJourney.LearningJourneyController.GetRecentSuggestions">
            <summary>
            Lấy danh sách 5 nhiệm vụ gần nhất của học sinh.
            </summary>
            <returns>
            - Ưu tiên nhiệm vụ có hạn nộp.
            - Nếu chưa đủ 5, bổ sung nhiệm vụ không có hạn nộp theo ngày tạo.
            </returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LearningJourney.LearningJourneyController.GetLearningTime(Hoclieu.HttpApi.Host.Controllers.LearningJourney.LearningTimeRequest)">
            <summary>
            Lấy tổng thời gian học (milliseconds), thời gian học theo từng ngày và trung bình thời gian học.
            </summary>
            <param name="request">Thông tin ngày bắt đầu và kết thúc để truy xuất dữ liệu học tập.</param>
            <returns>
            Trả về thông tin tổng thời gian học, thời gian học theo ngày và thời gian học trung bình mỗi ngày (đơn vị: milliseconds).
            </returns>
            <response code="200">Thành công - trả về dữ liệu thời gian học.</response>
            <response code="401">Không được xác thực.</response>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LearningJourney.LearningJourneyController.GetRecentBooks(System.Int32)">
            <summary>
            Lấy danh sách sách gần đây học sinh đã học, bao gồm số lượng kỹ năng và số kỹ năng đã hoàn thành.
            </summary>
            <param name="count">Số lượng sách cần lấy gần đây nhất.</param>
            <returns>Danh sách sách và tiến độ kỹ năng.</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.LiveClassroomController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LiveClassroomController.#ctor(Hoclieu.Classrooms.ClassroomRepository,Hoclieu.Users.StudentRepository,Hoclieu.Mongo.Service.MongoQuestionCacheRepository,Hoclieu.Services.ClassroomService,Hoclieu.Services.AnalyticService,AutoMapper.IMapper,Hoclieu.Mongo.Service.MongoQuestionRepository)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.LiveClassroomController.GetRecentAnsweredQuestions(System.Guid,System.Guid,System.DateTime)">
             <summary>
            
             </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Lms.LmsClassroomsController">
            <summary>
            Classroom API
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsClassroomsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.Lms.LmsClassroomService,Hoclieu.Services.User.TenancyService,Hoclieu.Services.ClassroomService)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsClassroomsController.GetListClassLmsByAdmin(Hoclieu.Classrooms.ClassroomFilterRequestAdmin)">
            <summary>
            List class  LMS for school manager
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsClassroomsController.GetListClassLmsByTeacher(Hoclieu.Classrooms.ClassroomFilterRequest)">
            <summary>
            List class  LMS
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsClassroomsController.GetClassLmsByStudent(Hoclieu.Classrooms.ClassroomFilterRequest)">
            <summary>
            student class
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsClassroomsController.GetClassroomOverview(System.Guid)">
            <summary>
            Get list classroom inactive
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsClassroomsController.GetClassroomOverall(Hoclieu.Classrooms.GetClassroomOverallRequest)">
            <summary>
            lấy báo cáo tổng quan cho lớp học
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController">
            <summary>
            Classroom API
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetSugesstionTeacherForClass(Hoclieu.Core.Dtos.Lms.Suggestion.GetSuggestionForClass,System.Guid)">
            <summary>
             Get suggestion of teacher for classroom
            </summary>
            <param name="request"></param>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetSuggestionOverall(Hoclieu.Core.Dtos.Lms.Suggestion.GetSuggestionOverall)">
            <summary>
            Get suggestion overall
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetSuggestionInfoTeacher(System.Guid,System.Collections.Generic.List{System.Guid})">
             <summary>
            
             </summary>
             <param name="suggestionId"></param>
             <param name="isOverall"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetSuggestionInfoStudent(System.Guid)">
            <summary>
            Get suggestionInfo of student
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetSuggestionStudentForClass(Hoclieu.Core.Dtos.Lms.Suggestion.GetSuggestionForClass,System.Guid)">
            <summary>
             Get suggestion of student for classroom
            </summary>
            <param name="request"></param>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetSuggestionMarkForClass(Hoclieu.Core.Dtos.Lms.Suggestion.GetSuggestionMarkRequest,System.Guid)">
            <summary>
            Get suggestion mark for classroom
            </summary>
            <param name="request"></param>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetListClassroomBeSuggestions">
            <summary>
            Get classroom by suggestion
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetSuggestionOverallMarkDashBoard(Hoclieu.Core.Dtos.Lms.Suggestion.GetSuggestionMarkOverallRequest)">
            <summary>
            Get suggestion overall mark for dashboard - hiển thị tối đa 10 nhiệm vụ chờ chấm
            Sắp xếp theo số bài cần chấm giảm dần, sau đó theo số học sinh đã làm giảm dần
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Lms.LmsSuggestionsController.GetExerciseHistoryBySkillResultId(System.Guid,System.Boolean,System.Nullable{System.Guid})">
            <summary>
            Get history do exercise of student by skillresult id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.EvaluationCriteriasController.CreateEvaluationCriteriaMulti(System.Collections.Generic.List{Hoclieu.Core.Dtos.MarkBook.EvaluationCriteriaRequest})">
            <summary>
            Tạo nhiều tiêu chí đánh giá
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.EvaluationCriteriasController.GetEvaluationCriteria(System.Int32)">
            <summary>
            Lấy thông tin tiêu chí đánh giá
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.EvaluationCriteriasController.PutEvaluationCriteria(System.Int32,Hoclieu.Core.Dtos.MarkBook.EvaluationCriteriaRequest)">
            <summary>
            chỉnh sửa tiêu chí đánh giá
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.EvaluationCriteriasController.DeleteEvaluationCriteria(System.Int32)">
            <summary>
            Xoá tiêu chí đánh giá
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.MarkBooksController.CreateConfigMarkBook(Hoclieu.Core.Dtos.MarkBook.ConfigMarkBookRequest)">
            <summary>
            Tạo config sổ đánh giá
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.MarkBooksController.GetAllConfig(Hoclieu.Core.Dtos.MarkBook.MarkBookFilter)">
            <summary>
            Lấy danh sách config sổ đánh giá
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.MarkBooksController.PutConfigMarkBook(System.Int32,Hoclieu.Core.Dtos.MarkBook.ConfigMarkBookRequest)">
            <summary>
            Sửa config sổ đánh giá
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.MarkBooksController.Delete(System.Int32)">
            <summary>
            Xoá config sổ đánh giá
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.MarkBook.MarkBooksController.GetSubjectMarkBook" -->
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.MarkBook.ValueAssessmentsController.HandleCreateOrUpdate(Hoclieu.Core.Dtos.MarkBook.ValueAssessmentRequest)" -->
        <member name="M:Hoclieu.HttpApi.Host.Controllers.MarkBook.ValueAssessmentsController.GetValueAssessment(Hoclieu.Core.Dtos.MarkBook.GetValueRequest)">
            <summary>
            Lấy giá trị theo lớp và môn học kỳ
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.NotificationSchoolsController.GetPaginationByTeacherOrStudent(Hoclieu.Core.Dtos.NotificationSchool.NotificationSchoolPaginationRequest)">
            <summary>
            lấy danh sách thông báo dành cho học sinh và giáo viên
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.NotificationSchoolsController.CancelNotification(System.Guid)">
            <summary>
            Huỷ gửi thông báo
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.OnLuyen.ReviewController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OnLuyen.ReviewController.#ctor(Hoclieu.Services.OnLuyen.ReviewService,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
             <summary>
            
             </summary>
             <param name="reviewService"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OnLuyen.ReviewController.GetListBookReview(Hoclieu.Core.Dtos.OnLuyen.ListBookReviewRequest)">
            <summary>
            API lấy danh sách sách HLTM
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OnLuyen.ReviewController.GetListSubjectsByGrade">
            <summary>
            get danh sách môn học theo khối lớp
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OnLuyen.ReviewController.GetListStudyProgrammeBookFilter(System.Nullable{System.Guid})">
             <summary>
            
             </summary>
             <param name="gradeId"></param>
             <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.#ctor(Hoclieu.Services.OnThi10.ExamBookProductService,Hoclieu.Mongo.Service.ExamBookProductRepository,Hoclieu.Services.OnThi10.ExamPreparationProductService,Hoclieu.Services.OnThi10.ExamCacheService,Hoclieu.Services.BookExtensiveResourceService,Hoclieu.Mongo.Service.ExamPreparationProductRepository,Hoclieu.Books.BookRepository,Hoclieu.OnThi10.ProductCategoryPermissionRepository)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetExamBookProduct(Hoclieu.OnThi10.ExamPreparationProductType)">
            <summary>
            API: Lấy Tài nguyên bổ trợ kèm sách (LV1)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.CreateExamBookProduct(Hoclieu.Core.Dtos.OnThi10.CreateOrUpdateExamBookProductRequest)">
            <summary>
            API: thêm hoặc sửa phần Tài nguyên bổ trợ kèm sách (LV1)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.CreateExamBookProductItem(Hoclieu.Core.Dtos.OnThi10.CreateOrUpdateExamBookProductItemRequest)">
            <summary>
            API: Thêm hoặc sửa phần sách trong Tài nguyên bổ trợ kèm sách (LV2)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.DeleteExamBookProduct(System.Guid)">
            <summary>
            API: Xoá Tài nguyên bổ trợ kèm sách (LV1)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.DeleteExamBookProductItem(System.Guid,System.Guid)">
            <summary>
            API: Xoá sách trong Tài nguyên bổ trợ kèm sách (LV2)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.ReorderExamBookProduct(Hoclieu.Core.Dtos.OnThi10.ReorderExamBookProductRequest)">
            <summary>
            reordering exam book product
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.ReorderExamBookProductItem(Hoclieu.Core.Dtos.OnThi10.ReorderExamBookProductItemRequest)">
            <summary>
            API: Sắp xếp lại sách trong Tài nguyên bổ trợ kèm sách (LV2)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetExamBookProducts(Hoclieu.OnThi10.ExamPreparationProductType)">
            <summary>
            API: Lấy danh sách Tài nguyên bổ trợ kèm sách (LV1), không kèm thông tin người dùng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetBookActivationInfo(Hoclieu.Core.Dtos.OnThi10.GetBookActivationInfoRequest)">
            <summary>
            API: Lấy thông tin kích hoạt sách của người dùng cho phần tài nguyên sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetBookActivationInfo(System.Guid,System.Nullable{System.Guid})">
            <summary>
            API: Lấy thông tin kích hoạt sách của người dùng cho phần tài nguyên sách của 1 sách cụ thể
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetBookResourceDetail(Hoclieu.Core.Dtos.OnThi10.GetBookResourceDetailRequest)">
            <summary>
            API: Lấy thông tin chi tiết tài nguyên sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetBookResourceInfo(System.Guid,System.Guid)">
            <summary>
            API: Lấy id của sáh chưa bộ câu hỏi mở rộng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetBookActivationInfoByBookId(System.Guid)">
            <summary>
            API: Láy 2 id của bộ đề và bộ sách từ id sách
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamBookProductsController.GetListExamCacheStatus(Hoclieu.Core.Dtos.OnThi10.GetBookResourceDetailRequest)">
            <summary>
            API: Kiểm tra trạng thái đề thi
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ExamCachesController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCachesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.ExamCacheRepository,Hoclieu.Services.OnThi10.ExamCacheService,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheKnowledgeRepository)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCachesController.PostAddExamCache(Hoclieu.Core.Dtos.Book.BookExamCache.PostAddExamCacheRequest)">
            <summary>
            API gán đề thi vào sách
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCachesController.CheckCode(System.String)">
            <summary>
            API check code đề thi tồn tại
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCachesController.UpdateExamCache(Hoclieu.Core.Dtos.Book.BookExamCache.UpdateExamCacheRequest)">
            <summary>
            Cập nhật thông tin đề thi: Name, Description, NumericalOrder, Time, TypeAccess, Code, ExamState
            </summary>
            <param name="examCacheDto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCachesController.ReorderExamCache(System.Guid,System.Guid)">
            <summary>
            Reorder exam cache
            </summary>
            <param name="sourceExamCacheId"></param>
            <param name="targetExamCacheId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCachesController.ReorderExamCacheByBookId(Hoclieu.Core.Dtos.Book.BookExamCache.ReorderExamCacheByBookIdRequest)">
            <summary>
            Sắp xếp lại thứ tự đề thi theo bookId
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCachesController.DeleteExamCache(System.Guid)">
            <summary>
            Xoá đề thi
            </summary>
            <param name="examCacheId"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.ExamCacheRepository,Hoclieu.Mongo.Service.MongoQuestionRepository,Hoclieu.Mongo.Service.MongoGroupContentRepository,Hoclieu.Mongo.Service.MongoCheckpointKnowledgeRepository,Hoclieu.Services.OnThi10.ExamCacheService,Hoclieu.Mongo.Service.ExamPreparationProductRepository,Hoclieu.Mongo.Service.ExamBookProductRepository,Hoclieu.Services.BookCodeService)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetExamCacheQuestions(System.Guid)">
            <summary>
            Lấy thông tin chi tiết đề thi
            </summary>
            <param name="examId"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetBookExam10">
            <summary>
            Lấy danh sách bộ sách ôn thi 10
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetBookSetExam10">
            <summary>
            Lấy danh sách bộ sách bộ đề ôn thi 10
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetExamCacheByBook(System.Guid)">
            <summary>
            Lấy danh sách đề thi theo sách
            </summary>
            <param name="bookId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetExamCacheBySkill(System.Guid)">
            <summary>
            Lấy danh sách đề thi đã gán của đề
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetDepartment">
            <summary>
            Lấy danh sách sở giáo dục
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetBookByDepartment(System.Guid)">
            <summary>
            Lấy danh sách bộ đề theo sở giáo dục
            </summary>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamCacheTestController.GetBookByDepartmentPriority(System.Guid,Hoclieu.Dtos.PagedAndSortedResultRequest)">
            <summary>
            Lấy danh sách bộ đề gợi ý các tỉnh theo thứ tự ưu tiên của sở đang chọn
            </summary>
            <param name="departmentId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.#ctor(Hoclieu.Services.OnThi10.ExamPreparationProductService,Hoclieu.Mongo.Service.ExamPreparationProductRepository,Hoclieu.Services.OnThi10.ExamCacheService,Hoclieu.Services.OnThi10.ExamBookProductService,Hoclieu.Services.BookExtensiveResourceService,Hoclieu.Books.BookRepository,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser})">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetAllExamPreparationProducts(Hoclieu.OnThi10.ExamPreparationProductType)">
            <summary>
            Lấy bộ đề thi của ôn thi 10 hoặc TNTHPT
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.CreateOrUpdateExamPreparationProduct(Hoclieu.Core.Dtos.OnThi10.UpdateExamPreparationProductRequest)">
            <summary>
            Thêm hoặc sửa bộ đề theo Sở
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.CreateOrUpdateSubject(System.Guid,Hoclieu.Core.Dtos.OnThi10.UpdateExamPreparationProductSubjectRequest)">
            <summary>
            Tạo hoặc sửa bộ đề theo môn học
            </summary>
            <param name="id"></param>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.CreateOrUpdateBook(System.Guid,System.Guid,Hoclieu.Core.Dtos.OnThi10.UpdateExamPreparationProductBookRequest)">
            <summary>
            Tạo hoặc sửa gói đề thi
            </summary>
            <param name="id"></param>
            <param name="examPreparationProductSubjectId"></param>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.ReorderProducts(Hoclieu.Core.Dtos.OnThi10.ReorderExamProductRequest)">
            <summary>
            Sắp xếp bộ đề theo Sở(Lv1)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.ReorderSubjects(Hoclieu.Core.Dtos.OnThi10.ReorderSubjectRequest)">
            <summary>
            API: Sắp xếp lại bộ đề theo môn(Lv2)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.ReorderBooks(Hoclieu.Core.Dtos.OnThi10.ReorderBookRequest)">
            <summary>
            API: Sắp xếp lại gói đề thi(Lv3)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.DeleteExamPreparationProduct(System.Guid)">
            <summary>
            Xóa bộ đề theo sở
            </summary>
            <param name="id"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.DeleteSubject(System.Guid,System.Guid)">
            <summary>
            Xóa bộ đề theo môn học
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.DeleteBook(System.Guid,System.Guid,System.Guid)">
            <summary>
            Xóa sách trong bộ đề
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetOverviewExamPreparationProducts(Hoclieu.Core.Dtos.OnThi10.ExamPreparationProduct.GetOverviewExamPreparationProductRequest)">
            <summary>
            API: Lấy thông tin bộ đề ở trang chủ ôn thi
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetListDepartment(Hoclieu.OnThi10.ExamPreparationProductType)">
            <summary>
            API: Lấy danh sách các tỉnh, TP hiện có bộ đề trong hệ thống theo sản phẩm
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetExamPreparationProductByDepartment(Hoclieu.Core.Dtos.OnThi10.ExamPreparationProduct.GetExamPreparationProductByDepartmentRequest)">
            <summary>
            API: Lấy thông tin bộ đề theo Sở
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetExamPreparationProductDetail(Hoclieu.Core.Dtos.OnThi10.ExamPreparationProduct.GetExamPreparationProductDetailRequest)">
            <summary>
            API: Lấy thông chi tiết bộ đề
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetExamPreparationProductActivation(System.Guid)">
            <summary>
            API: Lấy thông tin kích hoạt bộ đề
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetExamPreparationProductStatistic(System.Guid)">
            <summary>
            API: Thống kê % luyện tập
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamPreparationProductsController.GetExamPreparationProductStatus(System.Guid)">
            <summary>
            API: Kiểm tra trạng thái đề thi
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.ExamsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamsController.SubmissionExamAnswer(Hoclieu.Core.Dtos.OnThi10.ExamSubmissionAnswerRequest)">
            <summary>
            CheckpointSubmit
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamsController.SubmitExamAnswer(System.Guid)">
             <summary>
            
             </summary>
             <param name="examCacheId"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamsController.DeleteExamAnswer(System.Guid)">
             <summary>
            
             </summary>
             <param name="id"></param>
             <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamsController.StartExam(System.Guid)">
            <summary>
            Bắt đầu làm bài
            </summary>
            <param name="examId"></param>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.ExamsController.GetExamInfo(System.Guid,System.Boolean)">
            <summary>
            Lấy thông tin bài làm
            </summary>
            <param name="examCacheId"></param>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.TheoryController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TheoryController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.TheoryDataRepository,Hoclieu.Subjects.SubjectRepository)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TheoryController.GetTheoryData(Hoclieu.Dtos.TheoryFilterDto)">
            <summary>
            Lấy thông tin chi tiết đề thi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TheoryController.SaveTheoryData(Hoclieu.Dtos.AddTheoryDataRequest,System.Nullable{System.Guid})">
            <summary>
            Thêm hoặc cập nhật thông tin bài lý thuyết
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TheoryController.DeleteTheoryData(System.Guid)">
            <summary>
            Xóa bài lý thuyết
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TheoryController.GetAllTheoryData">
            <summary>
            Lấy thông tin các tai liệu
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TheoryController.UpdateTheoryData(System.Collections.Generic.List{Hoclieu.Dtos.UpdateTheoryDataRequest})">
            <summary>
            Cập nhật lý thuyết
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.OptimizeDataController">
            <summary>
            Controller
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OptimizeDataController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.FileService,Hoclieu.Services.SkillService,Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings},Hoclieu.Mongo.Service.MongoNewDataQuestionRepository)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OptimizeDataController.GenerateImageFromDataGroupSkillId(System.Guid,System.Boolean)">
            <summary>
            Lấy dữ liệu ảnh từ dữ liệu của sách
            </summary>
            <param name="bookId">Book Id</param>
            <param name="isGetImage">Bỏ qua mẫu chưa Teacher không</param>
            <returns>kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OptimizeDataController.DataOptimize(Hoclieu.Core.Dtos.DataOptimize.DataOptimizeDto)">
            <summary>
            Optimized data
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.BankTransactionsController">
            <summary>
            Quản lý giao dịch
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BankTransactionsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.AspNetCore.SignalR.IHubContext{Hoclieu.Hubs.ChatHub})">
            <summary>
            Hàm khởi tạo controller bank transaction
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BankTransactionsController.GetTransactionNewest">
            <summary>
            Lấy giao dịch mới nhất
            </summary>
            <returns>Thông tin giao dịch</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BankTransactionsController.CreateTransactions(Hoclieu.Banks.CreateTransactionsRequest)">
            <summary>
            Tạo mới giao dịch
            </summary>
            <param name = "request" >Dữ liệu tạo mới giao dịch</param>
            <returns>Kết quả tạo mới giao dịch</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.BankTransactionsController.CreateRsa">
            <summary>
            Tạo mới key
            </summary>
            <returns>key</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController">
            <summary>
            Quản lý chiết khấu thẻ
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Hàm khởi tạo controller card discount
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController.FindCardDiscounts(Hoclieu.Banks.FindCardDiscountRequest)">
            <summary>
            Tìm kiếm chiết khấu thẻ
            </summary>
            <param name = "request" >Dữ liệu tìm kiếm chiết khấu thẻ</param>
            <returns>Danh sách chiết khấu thẻ</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController.GetHistoryCardDiscounts(Hoclieu.Banks.FindCardDiscountRequest)">
            <summary>
            Lấy lịch sử thay đổi giá bán
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController.CreateCardDiscount(Hoclieu.Banks.CreateCardDiscountRequest)">
            <summary>
            Tạo mới chiết khấu thẻ
            </summary>
            <param name = "request" >Dữ liệu tạo mới chiết khấu thẻ</param>
            <returns>Kết quả tạo mới chiết khấu thẻ</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController.GetCardDiscount(System.Guid)">
            <summary>
            Lấy dữ liệu chi tiết chiết khấu thẻ
            </summary>
            <param name = "id" >Id chiết khấu thẻ</param>
            <returns>Dữ liệu chi tiết chiết khấu thẻ</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController.UpdateCardDiscount(System.Guid,Hoclieu.Banks.CreateCardDiscountRequest)">
            <summary>
            Cập nhật chiết khấu thẻ
            </summary>
            <param name = "id" >Id chiết khấu thẻ</param>
            <param name = "request" >Dữ liệu cập nhật chiết khấu thẻ</param>
            <returns>Kết quả cập nhật chiết khấu thẻ</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.CardDiscountsController.DeleteCardDiscount(System.Guid)">
            <summary>
            Xóa chiết khấu thẻ
            </summary>
            <param name = "id" >Id chiết khấu thẻ</param>
            <returns>Kết quả xóa chiết khấu thẻ</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.OrdersController">
            <summary>
            Quản lý đơn hàng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OrdersController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.VnPaymentService,Hoclieu.Services.ZaloPayService,Hoclieu.Services.VietQRService)">
            <summary>
            Hàm khởi tạo controller order
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OrdersController.FindOrders(Hoclieu.Banks.FindOrderRequest)">
            <summary>
            Tìm kiếm đơn hàng của chính mình
            </summary>
            <param name = "request" >Dữ liệu tìm kiếm đơn hàng</param>
            <returns>Danh sách đơn hàng</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OrdersController.FindAllOrders(Hoclieu.Banks.FindOrderRequest)">
            <summary>
            Tìm kiếm đơn hàng của tất cả người dùng
            </summary>
            <param name = "request" >Dữ liệu tìm kiếm đơn hàng</param>
            <returns>Danh sách đơn hàng</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OrdersController.FindUserOrders(System.String,Hoclieu.Banks.FindOrderRequest)">
            <summary>
            Lấy đơn hàng của người dùng
            </summary>
            <param name="request"></param>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OrdersController.CreateOrder(Hoclieu.Banks.CreateOrderRequest)">
            <summary>
            Tạo mới đơn hàng
            </summary>
            <param name = "request" >Dữ liệu tạo mới đơn hàng</param>
            <returns>Kết quả tạo mới đơn hàng</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.OrdersController.GetOrder(System.Guid)">
            <summary>
            Thông tin chi tiết đơn hàng của người dùng
            </summary>
            <param name = "id" >Id đơn hàng</param>
            <returns>Thông tin chi tiết đơn hàng</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.PackagesController">
            <summary>
            Quản lý gói hàng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Hàm khởi tạo controller package
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.FindPackages(Hoclieu.Banks.FindPackageRequest)">
            <summary>
            Tìm kiếm gói
            </summary>
            <param name = "request" >Dữ liệu tìm kiếm gói</param>
            <returns>Danh sách gói</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.CreatePackage(Hoclieu.Banks.CreatePackageRequest)">
            <summary>
            Tạo mới gói
            </summary>
            <param name = "request" >Dữ liệu tạo mới gói</param>
            <returns>Kết quả tạo mới gói</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.GetPackage(System.Guid)">
            <summary>
            Lấy dữ liệu chi tiết gói
            </summary>
            <param name = "id" >Id gói</param>
            <returns>Dữ liệu chi tiết gói</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.UpdatePackage(System.Guid,Hoclieu.Banks.CreatePackageRequest)">
            <summary>
            Cập nhật gói
            </summary>
            <param name = "id" >Id gói</param>
            <param name = "request" >Dữ liệu cập nhật gói</param>
            <returns>Kết quả cập nhật gói</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.DeletePackage(System.Guid)">
            <summary>
            Xóa gói
            </summary>
            <param name = "id" >Id gói</param>
            <returns>Kết quả xóa gói</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.AddBook(System.Guid,System.Guid)">
            <summary>
            Thêm sách trong gói
            </summary>
            <param name = "id" >Id gói</param>
            <param name = "bookId" >Id gói</param>
            <returns>Kết quả thêm sách trong gói</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.RemoveBook(System.Guid,System.Guid)">
            <summary>
            Xóa sách trong gói
            </summary>
            <param name = "id" >Id gói</param>
            <param name = "bookId" >Id gói</param>
            <returns>Kết quả xóa sách trong gói</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.GetPackagesInCart">
            <summary>
            Lấy danh sách gói trong giỏ hàng
            </summary>
            <returns>Danh sách gói trong giỏ hàng</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.AddPackageToCart(System.Guid)">
            <summary>
            Thêm gói vào giỏ hàng
            </summary>
            <param name = "id" >Id gói</param>
            <returns>Kết quả thêm gói vào giỏ hàng</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PackagesController.DeletePackageFromCart(System.Guid)">
            <summary>
            Xóa gói từ giỏ hàng
            </summary>
            <param name = "id" >Id gói</param>
            <returns>Kết quả xóa gói từ giỏ hàng</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.PaymentsController">
            <summary>
            VNPAY
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PaymentsController.#ctor(Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.VnPaymentSettings},Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.ZaloPaySettings},Hoclieu.Services.EmailService,Hoclieu.Services.ApplePaymentService,Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Hàm khỏi tạo payment controller
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PaymentsController.VnPaymentIPN(Hoclieu.Core.Dtos.VnPayment.IPNParams)">
            <summary>
            Ghi nhận kết quả thanh toán từ VNPAY
            </summary>
            <param name="ipnParams"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PaymentsController.VnPaymentReturn(Hoclieu.Core.Dtos.VnPayment.IPNParams)">
            <summary>
            Kiểm tra thanh toán từ VnPay Return
            </summary>
            <param name="ipnParams"></param>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PaymentsController.ZaloPayCallback(System.Object)">
            <summary>
            Kiểm tra thanh toán từ zalopay callback
            </summary>
            <param name="cbdata"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PaymentsController.VerifyReceipt(Hoclieu.Core.Dtos.ApplePayment.VerifyReceiptRequest)">
            <summary>
            Kiểm tra thanh toán từ App Store Receipt
            </summary>
            <param name="request"></param>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController">
            <summary>
            Quản lý quyền của người dùng biên tập
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.EntityFrameworkCore.Permission.PermissionRepository,Hoclieu.EntityFrameworkCore.Permission.PermissionGroupRepository,Hoclieu.EntityFrameworkCore.Permission.PermissionUserRepository)">
            <summary>
            hàm khởi tạo controller quản lý quyền của người dùng biên tập
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.ImportData(System.String,System.String)">
            <summary>
            1. Import Permistion từ gg sheet
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.ReviewData(System.String,System.String)">
            <summary>
            review  permistion từ gg sheet, lấy moi sheet
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.EditPermission(Hoclieu.Core.Dtos.Permission.PermissionDto)">
            <summary>
            2. Thêm Permission + 3. Sửa Permission
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.DeletePermission(System.Int32)">
            <summary>
            4. Xóa Permission
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.GetAllPermission">
            recursion **** tham khao all-tree
            <summary>
            5. Lấy ra các quyền có trên hệ thống, kết quả trả về theo cây thư mục.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.ExportPermission">
            <summary>
            Lấy ra các quyền có trên hệ thống, kết quả trả về dạng list
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.ExportPermissionById(System.Int32)">
            <summary>
            Lấy ra các quyền có trên hệ thống, kết quả trả về dạng list
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.AddPermissionGroup(Hoclieu.Core.Dtos.Permission.PermissionGroupDto)">
            <summary>
            6. Thêm mới PermissionGroup
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.EditPermissionGroup(Hoclieu.Core.Dtos.Permission.PermissionGroupDto)">
            <summary>
            7. Sửa PermissionGroup
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.DeleteGroup(System.Int32)">
            <summary>
            Xóa PermissionGroup by Id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.AllPermissionGroups">
            <summary>
            Lấy danh sách PermissionGroup
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.AddPermissionUser(Hoclieu.Core.Dtos.Permission.PermissionUserRequest)">
            <summary>
            8. Tạo mới PermissionUser (có thêm người dùng theo email hoặc userName)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.DeletePermissionUser(System.Int32)">
            <summary>
            9. Xóa PermissionUser
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.GetListPermissionUser">
            <summary>
            10. Lấy ra danh sách người dùng đã được gán quyền
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.GetPermissionUserDetails(System.Int32)">
            <summary>
            11. Lấy chi tiết PermissionUser
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserPermissionManagementsController.UpdatePermissionUser(Hoclieu.Core.Dtos.Permission.PermissionUserRequest)">
            <summary>
            12. Update permission user
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.PublishersController">
            <summary>
            Quản lý nhà xuất bản
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PublishersController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,AutoMapper.IMapper)">
            <summary>
            Hàm khỏi tạo PublishersController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PublishersController.GetPublishers(System.String)">
            <summary>
            Lấy danh sách nhà xuất bản
            </summary>
            <param name = "name" >Dữ liệu bộ lọc</param>
            <returns>Danh sách nhà xuất bản</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PublishersController.CreatePublisher(Hoclieu.Core.Dtos.Publisher.CreatePublisherRequest)">
            <summary>
            Tạo nhà xuất bản
            </summary>
            <param name = "request" >Dữ liệu tạo mới nhà xuất bản</param>
            <returns>Thông tin nhà xuất bản</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PublishersController.UpdatePublisher(System.Guid,Hoclieu.Core.Dtos.Publisher.CreatePublisherRequest)">
            <summary>
            Chỉnh sửa nhà xuất bản
            </summary>
            <param name = "id" >ID nhà xuất bản</param>
            <param name = "request" >Dữ liệu chỉnh sửa nhà xuất bản</param>
            <returns>Thông tin nhà xuất bản</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.PublishersController.RakeBookPublisers">
            <summary>
            Gán nhà xuất bản học liệu .vn vào tất cả các sách
            </summary>
            <returns>Kết quả</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultsController">
            <summary>
            Controller for managing quality evaluation results.
            Provides endpoints to create, read, update, and delete quality evaluation results.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultsController.#ctor(Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultService)">
            <summary>
            Initializes a new instance of the <see cref="T:Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultsController"/> class.
            </summary>
            <param name="service">The service for managing quality evaluation results.</param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultsController.UpdateSingle(Hoclieu.Core.Dtos.QualityEvaluationResult.QualityEvaluationResultRequest)">
            <summary>
            Creates or updates a quality evaluation record for a specific ability or quality.
            </summary>
            <param name="request">The request containing the quality evaluation data.</param>
            <returns>The created or updated <see cref="T:Hoclieu.Mongo.Document.QualityEvaluationResult.QualityEvaluationResult"/>.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultsController.GetByClassroomStudentId(System.Guid,Hoclieu.Core.Enums.SemesterExtra)">
            <summary>
            Retrieves quality evaluation results for a classroom and semester.
            </summary>
            <param name="classroomId">The ID of the classroom.</param>
            <param name="semester">The semester to filter by.</param>
            <returns>A list of quality evaluation results.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultsController.GetGeneralLedgerByClassroom(Hoclieu.Core.Dtos.QualityEvaluationResult.GeneralLedgerQuery)">
            <summary>
            Retrieves general ledger data for a classroom and semester.
            </summary>
            <param name="request">The query parameters for the general ledger request.</param>
            <returns>A list of <see cref="T:Hoclieu.Core.Dtos.QualityEvaluationResult.GeneralLedgerResponse"/> objects containing ledger data.</returns>
            <exception cref="T:System.ArgumentException">Thrown when the specified semester is invalid.</exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QualityEvaluationResult.QualityEvaluationResultsController.PostGeneralLedger(Hoclieu.Core.Dtos.QualityEvaluationResult.GeneralLedgerRequest)">
            <summary>
            Creates or updates a general ledger record for a specific classroom student and semester.
            </summary>
            <param name="request">The request containing the general ledger data.</param>
            <returns>The created or updated <see cref="T:Hoclieu.Mongo.Document.QualityEvaluationResult.GeneralLedger"/>.</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.QuestionImportsController">
            <summary>
            Controller for question import
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QuestionImportsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.QuestionImportService,Hoclieu.Services.TemplateQuestionService,Hoclieu.Mongo.Service.MongoQuestionCacheRepository,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser})">
            <summary>
            constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QuestionImportsController.ImportData(System.Guid)">
            <summary>
            import data from gg doc
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController">
            <summary>
             Report for classroom
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetClassroomOverall(Hoclieu.Classrooms.TeacherClassroomReportRequest)">
            <summary>
            lấy báo cáo tổng quan cho lớp học
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetSchoolOverall(Hoclieu.Classrooms.TeacherClassroomReportRequest)">
            <summury>
            Lấy tổng quan cho tài khoản trường
            </summury>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetDisentangleOfSchool(Hoclieu.Classrooms.TeacherClassroomReportRequest)">
            <summary >
            disentangle of school
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetStudentReport(Hoclieu.Core.Dtos.Classroom.StudentReportRequest)">
            <summary>
            lấy báo cáo học sinh trong lớp
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetAssignExercise(Hoclieu.Core.Dtos.Classroom.StudentAssigmentRequest)">
            <summary>
            lấy bài đã giao cho học sinh
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetAllAssignExercise(Hoclieu.Core.Dtos.Classroom.ClassroomStudentAllAssigmentRequest)">
            <summary>
            lấy tất cả bài đã giao cho học sinh role giáo viên
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetOneAssignExerciseAllStudent(Hoclieu.Core.Dtos.Classroom.ClassroomTeacherAllAssigmentRequest)">
            <summary>
            lấy một bài đã giao cho tất cả học sinh role giáo viên
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ClassroomReportController.GetClassroomPracticeProgress(Hoclieu.Core.Dtos.Classroom.StudentReportRequest)">
            <summary>
            Lấy tiến trình luyện tập của lớp học
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Report.ReportController">
            <summary>
             Thống kê
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetTecherStatistic(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            lấy giáo viên theo SPT
            </summary>
            <param name="search"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDepartmentOverview(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Tổng quan sở
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDeployDepartment(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Lấy danh sách phòng triển khai
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDesployOfSPTSchool(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Lấy danh sách trường deploy
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetReportSkillResult(Hoclieu.DepartmentManagers.FilterDepartmentRequest)">
            <summary>
            Làm chủ kỹ năng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetTopSchoolDepartment(Hoclieu.DepartmentManagers.FilterDepartmentRequest)">
            <summary>
            Top 5 school
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetTopStudentDepartment(Hoclieu.DepartmentManagers.FilterDepartmentRequest)">
            <summary>
            Top 5 students
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetReportDepartmentTimeline(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Report-Timeline
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDepartmentSearch(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Lấy danh sach phòng thuộc sở và search
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDetailForExcel(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            down excel report assgin
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetExcelExportByDepartmentId(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            download export excel active
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetSchoolOverView(Hoclieu.Core.Dtos.DepartmentManager.FilterSchoolRequest)">
            <summary>
            Tổng quan báo cáo trường
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetTopClass(Hoclieu.DepartmentManagers.FilterDepartmentRequest)">
            <summary>
            Top 5 lớp
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.ExportDepartmentAverageQuestion(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Lấy dữ liệu xuất excel của báo cáo câu hỏi trung bình
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.ExportDepartmentTimeline(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Lấy dữ liệu xuất excel của báo cáo thời gian học
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetSchools(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Lấy danh sách trường của sở
            Lấy từ cache của overview
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetEfficiencySkillResult(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Báo cáo làm chủ kỹ năng
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetEfficiencyAverageQuestion(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Câu hỏi trung bình của phần hiệu quả
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetStudentList(Hoclieu.DepartmentManagers.FilterDepartmentRequest)">
            <summary>
            Lấy danh sách học sinh
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetTeacherList(Hoclieu.DepartmentManagers.FilterDepartmentRequest)">
            <summary>
            Lấy danh sách giáo viên
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDeployByClassroom(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            Lấy danh sách lớp học triển khai
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDeployByTeacher(Hoclieu.Core.Dtos.Report.FindReportRequest)">
            <summary>
            lấy danh sách giáo viên
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.GetDepartmentInMaterial">
            <summary>
            Lấy danh sách các sở có trong học liệu
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Report.ReportController.getUserReportDepartmentAssignment(System.String)">
            <summary>
            Lấy danh sách user cùng với các sở mà user được gán quyền xem báo cáo
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.RoleStyles.StylesController.GetAll">
            <summary>
            Lấy tất cả các css
            </summary>
            <returns>Danh sách tất css</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.RoleStyles.StylesController.GetById(System.Guid)">
            <summary>
            Lấy css theo định danh
            </summary>
            <param name="id">Định danh Css</param>
            <returns>Dữ liệu css</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.RoleStyles.StylesController.GetMyStyles">
             <summary>
            
             </summary>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.RoleStyles.StylesController.Add(Hoclieu.Core.Dtos.RoleStyles.StyleDto)">
            <summary>
            Thêm css cho các role
            </summary>
            <param name="request">Dữ liệu css</param>
            <returns>Dữ liệu css</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.RoleStyles.StylesController.Edit(System.Guid,Hoclieu.Core.Dtos.RoleStyles.StyleDto)">
            <summary>
            Sửa css cho các role
            </summary>
            <param name="id">Định danh css</param>
            <param name="request">Dữ liệu thay đổi của css</param>
            <returns>Dữ liệu css cập nhật</returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.RoleStyles.StylesController.Edit(System.Guid)">
            <summary>
            Xoá css của các role theo định danh của chúng
            </summary>
            <param name="id">Định danh css</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.SingleSignOn.MatificSSOController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SingleSignOn.MatificSSOController.#ctor(Microsoft.Extensions.Options.IOptions{Hoclieu.Services.Settings.MatificSettings},Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Microsoft.Extensions.Caching.Distributed.IDistributedCache)">
            <summary>
            Initializes a new instance of the <see cref="T:Hoclieu.HttpApi.Host.Controllers.SingleSignOn.MatificSSOController"/> class.
            </summary>
            <param name="matificSettings"></param>
            <param name="userManager"></param>
            <param name="cache"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SingleSignOn.MatificSSOController.GenerateLoginUrl(Hoclieu.Core.Dtos.SingleSignOn.LoginMatificDto)">
            <summary>
            Generates a login URL for Matific SSO.
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SingleSignOn.MatificSSOController.VerifyToken(System.String)">
            <summary>
            Verifies the token provided by Matific and returns user ID.
            </summary>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController">
            <summary>
            Controller skill exam suggestion
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.DataQuestionService,Hoclieu.Services.SkillExamSuggestionService,Hoclieu.Services.SkillService,Hoclieu.Services.T2SService,Hoclieu.Services.CheckpointService,Hoclieu.Mongo.Service.MongoQuestionRepository,Hoclieu.Services.QuestionKnowledgeService,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository,Hoclieu.Mongo.Service.MongoGroupContentRepository,Hoclieu.Mongo.Service.MongoNewDataQuestionRepository,Hoclieu.Mongo.Service.MongoNewTemplateQuestionRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheInfoRepository,Hoclieu.Mongo.Service.MongoCheckpointKnowledgeRepository,Hoclieu.Mongo.Service.SkillExamSuggestionQuestionCustomCacheRepository)">
            <summary>
            Hàm khởi tạo
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetQuestionExam(System.Guid,Hoclieu.Core.Enums.ClientType)">
            <summary>
            API lấy câu hỏi của đề kiểm tra đc giao
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.ShareURlToStudent(System.Guid,Hoclieu.Core.Enums.ClientType)">
            <summary>
            api when teacher share url
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.SubmissionAnswer(Hoclieu.Checkpoints.CheckpointSubmissionAnswerRequest)">
            <summary>
            api submit dap an
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.Submission(System.Guid)">
            <summary>
            API nop bai
            </summary>
            <param name="skillExamSuggestionCacheId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetSkillExamAnalytic(System.Guid,Hoclieu.Core.Dtos.CheckpointAnalyticQuery)">
            <summary>
            API thống kê điểm luyện tập trung bình
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetSkillExamResultRecent(System.Guid,System.Int32,System.Int32)">
            <summary>
            API xem lich su lam bai
            </summary>
            <param name="skillSuggestionId"></param>
            <param name="skipCount"></param>
            <param name="maxResultCount"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetQuestionHistory(System.Guid)">
            <summary>
            API lấy câu hỏi của đề kiểm tra đc giao
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetExcerciseHistoriesAsync(System.Guid)">
            <summary>
            View history do exercise
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetCheckpointSkillTopWrong(System.Guid)">
            <summary>
            lấy thông tin các nội dung hay sai
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetCheckpointRanking(System.Guid)">
            <summary>
            lấy thông tin xếp hạng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetTopWrongSkills(System.Guid)">
            <summary>
            Thống kê ký năng hay sai của các học sinh trong lớp
            </summary>
            <param name="skillSuggestionId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SkillExamSuggestionController.GetCheckpointAnalyticByStudent(System.Guid,System.Guid)">
            <summary>
            API thong ke chi tiet cua hoc sinh
            </summary>
            <param name="skillSuggestionId"></param>
            <param name="studentId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TitleListQuestionController.GetTitleListQuestions(Hoclieu.Core.Dtos.TitleListQuestion.TitlelistQuestionRequest)">
            <summary>
            Lấy nhóm câu hỏi theo lessonId
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TitleListQuestionController.GetQuestionByTitleListQuestionId(System.Int64)">
            <summary>
            Lấy danh sách câu hỏi theo nhóm ai
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TitleListQuestionController.RateQuestion(System.Int64,Hoclieu.Core.Dtos.Skill.GroupQuestion.RateQuestionRequest)">
            <summary>
            Đánh giá câu hỏi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TitleListQuestionController.HistoryRateQuestionDtos(Hoclieu.Core.Dtos.Skill.GroupQuestion.ListRateQuestionRequest)">
            <summary>
            Lấy danh sách đánh giá câu hỏi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TitleListQuestionController.HistoryRateQuestionNotAnswerDtos(Hoclieu.Core.Dtos.Skill.GroupQuestion.ListRateQuestionRequest)">
            <summary>
            Lấy danh sách đánh giá không có answer question
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.StatisticController">
            <summary>
            Thống kê dữ liệu
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StatisticController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.MongoSkillResultRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository)">
            <summary>
            ham khoi tao
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StatisticController.GetSchoolStatistic(System.Collections.Generic.List{System.String})">
            <summary>
            Lấy thông kê trường học
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.StatusController">
            <summary>
            Controller
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StatusController.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StatusController.GetStatus">
            <summary>
            API get status service
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController">
            <summary>
            Quản lý chương trình học
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.BookService)">
            <summary>
            Hàm khởi tạo controller stydy programmes
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.Create(Hoclieu.StudyProgrammes.CreateStudyProgrammeRequest)">
            <summary>
            Tạo chương trình học mới
            </summary>
            <param name = "request" >Dữ liệu chương trình học</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.Update(System.Guid,Hoclieu.StudyProgrammes.UpdateStudyProgrammeRequest)">
            <summary>
            Cập nhật chương trình học
            </summary>
            <param name = "id" >Id chương trình học</param>
            <param name = "request" >Dữ liệu chương trình học</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.Delete(System.Guid)">
            <summary>
            Xoá chương trình học
            </summary>
            <param name = "id" >Id chương trình học</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.GetAllTree(Hoclieu.StudyProgrammes.GetStudyProgrammeAllTreeRequest)">
            <summary>
            Lấy cây cấu trúc chương trình học
            </summary>
            <returns>Cây cấu trúc</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.AddBook(System.Guid,Hoclieu.StudyProgrammes.StudyProgrammeAddBookRequest)">
            <summary>
            Thêm sách vào chương trình học
            </summary>
            <param name = "id" >Id chương trình học</param>
            <param name = "request" >Dữ liệu sách</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.UpdateBook(System.Guid,System.Guid,Hoclieu.StudyProgrammes.StudyProgrammeUpdateBookRequest)">
            <summary>
            Cập nhật sách trong chương trình học
            </summary>
            <param name = "id" >Id chương trình học</param>
            <param name = "bookId" >Id sách</param>
            <param name = "request" >Dữ liệu sách</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.DeleteBook(System.Guid,System.Guid)">
            <summary>
            Xoá sách khỏi chương trình học
            </summary>
            <param name = "id" >Id chương trình học</param>
            <param name = "bookId" >Id sách</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.SortStudyProgrammes(Hoclieu.StudyProgrammes.StudyProgrammeSortBooksRequest)">
            <summary>
            Sắp xếp chương trình học
            </summary>
            <param name = "request" >Dữ liệu sắp xếp</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.SortBooks(Hoclieu.StudyProgrammes.StudyProgrammeSortBooksRequest)">
            <summary>
            Sắp xếp sách trong chương trình học
            </summary>
            <param name = "request" >Dữ liệu sắp xếp</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.GetStudyProgrammeExtentiveResources(System.Guid)">
            <summary>
            Lấy extensive reources của study programme theo studyProgrammeId.
            </summary>
            <param name="studyProgrammeId">study programme id</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.GetStudyProgrammeExtentiveResourcesByBookId(System.Guid)">
            <summary>
            Lấy extensive reources của study programme theo bookId.
            </summary>
            <param name="studyProgrammeId">study programme id</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.GetExtentiveResourcesByBookId(System.Guid)">
            <summary>
            Lấy extensive reources của StudyProgrammeExtensiveResourceBooks theo bookId.
            </summary>
            <param name="bookId">study programme id</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.GetStudyProgrammeExtensiveResource(System.Int32)">
            <summary>
            get by id
            </summary>
            <param name="extensiveResourceId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.CreateStudyProgrammeExtensiveResource(System.Guid,Hoclieu.StudyProgrammes.StudyProgrammeExtensiveResourceRequest)">
            <summary>
            Tạo mới study programme extensive resource.
            </summary>
            <param name="studyProgrammeId">study programme id</param>
            <param name="request"></param>
            <returns>id của study programme extensive resource mới thêm</returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.CheckBookWhileAddNewStudyProgrammeExtensiveResource(System.Guid)">
            <summary>
            Lấy thông tin sách tạm thời khi add mới study programme extensive resource.
            </summary>
            <param name="bookId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.UpdateStudyProgrammeExtensiveResource(System.Guid,System.Int32,Hoclieu.StudyProgrammes.StudyProgrammeExtensiveResourceRequest)">
            <summary>
            Sửa study programme extensive resource.
            </summary>
            <param name="studyProgrammeId"></param>
            <param name="extensiveResourceId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.DeleteStudyProgrammeExtensiveResource(System.Guid,System.Int32)">
            <summary>
            Xóa study programme extensive resource.
            </summary>
            <param name="studyProgrammeId"></param>
            <param name="extensiveResourceId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.SortStudyProgrammeExtensiveResource(Hoclieu.StudyProgrammes.StudyProgrammeExtensiveResourceSortRequest)">
            <summary>
            Sắp xếp study programme extensive resource.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.CreateStudyProgrammeExtensiveResourceBook(Hoclieu.StudyProgrammes.StudyProgrammeExtensiveResourceBookRequest)">
            <summary>
            Tạo mới study programme extensive resource book.
            </summary>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.DeleteStudyProgrammeExtentiveResourceBook(System.Int32)">
            <summary>
            Xóa study programme extensive resource book.
            </summary>
            <param name="extensiveResourceBookId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.StudyProgrammesController.CheckAccessExtensiveResource(System.Int32)">
            <summary>
            Check extensive resource can access
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SubjectsController.UpdateSubjectOrder(System.Collections.Generic.List{Hoclieu.Subjects.SubjectDto})">
            <summary>
            API cập nhật thứ tự môn học và xoá cache môn cũ
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SuggestionData.SuggestionDataController.GetWorksheetSuggestions(System.Guid,System.Nullable{System.Guid})">
            <summary>
            API lấy thông tin những lớp được giao bài
            </summary>
            <param name="skillId"></param>
            <param name="classroomId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.SuggestionData.SuggestionDataController.CheckRevokeClassroomSuggestions(System.Guid,System.Guid)">
            <summary>
            Lấy dữ liệu làm bài của học sinh
            </summary>
            <param name="classroomId"></param>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.ClassroomService,Hoclieu.Classrooms.ClassroomRepository,Hoclieu.Classrooms.ClassroomTeacherRepository,Hoclieu.Classrooms.ClassroomStudentRepository)">
             <summary>
            
             </summary>
             <param name="dbContext"></param>
             <param name="classroomService"></param>
             <param name="classroomRepository"></param>
             <param name="classroomTeacherRepository"></param>
             <param name="classroomStudentRepository"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.GetMergedClassroom(System.Nullable{System.Guid})">
            <summary>
            Lấy danh sách lớp học là lớp ghép
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.UpdateClassroom(System.Guid,Hoclieu.Core.Dtos.Tenant.UpdateClassroomDto)">
            <summary>
            Cập nhật lớp học
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.GetAssignedTeachers(System.Guid)">
            <summary>
            Lấy danh sách giáo viên đã được phân công vào lớp học.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.GetAvailableTeachers">
            <summary>
            Lấy danh sách toàn bộ giáo viên thuộc tenant.
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.GetAssignedStudents(System.Guid)">
            <summary>
            Lấy chi tiết học sinh của lớp học theo classroomId.
            </summary>
            <param name="classroomId">ID của lớp học.</param>
            <returns>Chi tiết học sinh của lớp học.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.GetAvailableStudents(System.Boolean)">
            <summary>
            Lấy ra danh sách học sinh chưa được phân công.
            </summary>
            <returns>Chi tiết học sinh của lớp học.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.CreateRangeClassroom(System.Collections.Generic.List{Hoclieu.Core.Dtos.Tenant.CreateTenantClassroomRequest})">
            <summary>
            Tạo danh sách lớp học cho tenant với mã tenant và danh sách yêu cầu tạo lớp học.
            </summary>
            <param name="requests">Danh sách yêu cầu tạo lớp học.</param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.DeleteClassroom(System.Guid)">
            <summary>
            Xóa một lớp học theo classroomId và tenantCode.
            </summary>
            <param name="classroomId">ID của lớp học cần xóa.</param>
            <param name="tenantCode">Mã tenant.</param>
            <returns>Kết quả xóa lớp học.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.GetSelectedSubjectIdsByGradeAndSemester(System.Guid,Hoclieu.Core.Enums.Semester)">
            <summary>
            Lấy danh sách SubjectId theo các lớp thuộc khối và học kỳ.
            </summary>
            <param name="gradeId">ID của khối lớp.</param>
            <param name="semester">Học kỳ cần lọc.</param>
            <returns>
            Trả về danh sách các lớp và các SubjectId đã được chọn theo học kỳ.
            </returns>
            <response code="200">Thành công, trả về danh sách lớp và môn học.</response>
            <response code="400">Thiếu tenant hoặc không tìm thấy tenant.</response>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.UpdateClassroomSubjects(Hoclieu.Core.Dtos.Tenant.UpdateClassroomSubjectsRequest)">
            <summary>
            Cập nhật danh sách môn học cho các lớp học theo yêu cầu.
            </summary>
            <param name="request">Yêu cầu cập nhật môn học cho lớp học.</param>
            <returns>Kết quả cập nhật môn học cho các lớp học.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.GetAssignedSubjectsByTeacher(Hoclieu.Core.Dtos.Tenant.GetTeacherAssignmentsRequest)">
            <summary>
            Lấy danh sách ClassroomSubjectId mà giáo viên đã được phân công, trong các lớp chỉ định,
            kèm điều kiện giáo viên phải là giáo viên của lớp đó.
            </summary>
            <returns>
            Trả về danh sách ClassroomSubjectId mà giáo viên đã được phân công trong các lớp truyền vào.
            Kể cả khi không có ClassroomSubjectIds thì vẫn trả về classroomId với mảng rỗng.
            </returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantClassroomController.UpdateClassroomSubjectTeachers(Hoclieu.Core.Dtos.Tenant.UpdateClassroomSubjectTeacherRequest)">
            <summary>
            Cập nhật giáo viên được phân công cho các môn học của lớp học.
            </summary>
            <param name="request">Yêu cầu cập nhật giáo viên cho các môn học của lớp học.</param>   
            <returns>Kết quả cập nhật giáo viên cho các môn học của lớp học.</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Microsoft.AspNetCore.Identity.RoleManager{Hoclieu.Domain.User.ApplicationRole},Hoclieu.Services.User.TenancyService,Hoclieu.Services.User.TenancyUserManager)">
             <summary>
            
             </summary>
             <param name="dbContext"></param>
             <param name="userManager"></param>
             <param name="roleManager"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.GetAll(System.Int32,System.Int32)">
            <summary>
            Get all tenants
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.GetById(System.Int64)">
            <summary>
            Get tenant by id
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.Create(Hoclieu.Core.Dtos.Tenant.TenantDto)">
            <summary>
            Create a new tenant
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.Update(System.Int64,Hoclieu.Core.Dtos.Tenant.TenantDto)">
            <summary>
            Update a tenant
            </summary>
            <param name="id"></param>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.Delete(System.Int64)">
            <summary>
            Delete a tenant
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.AddRangeUsersToTenant(System.Int64,Hoclieu.Core.Dtos.Tenant.AddRangeUsersToTenantRequest)">
            <summary>
            Add range users to tenant
            </summary>
            <param name="tenantId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.GetCurrentTenantDetails(System.String)">
             <summary>
            
             </summary>
             <param name="HostName"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantController.GetDashboardIframeUrlAsync">
             <summary>
            
             </summary>
             <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantParentController">
            <summary>
            Controller quản lý phụ huynh trong cơ sở giáo dục
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantParentController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.UserService,Hoclieu.Services.User.TenancyUserManager)">
            <summary>
            Controller quản lý phụ huynh trong cơ sở giáo dục
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantParentController.AddRange(Hoclieu.Core.Dtos.Tenant.ImportParentRequest)">
            <summary>
            Import tạo tài khoản phụ huynh
            </summary>
            <param name="request">Dữ liệu tạo tài khoản phụ huynh</param>
            <returns>Thông báo tạo tài khoản phụ huynh</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantRegistrationController.Create(TenantRegistrationDto)">
            <summary>
            Create a new tenant registration
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantRegistrationController.GetAll">
            <summary>
            Get all tenant registrations
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantRegistrationController.GetById(System.Guid)">
            <summary>
            Get a tenant registration by id
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantRegistrationController.Update(System.Guid,TenantRegistrationDto)">
            <summary>
            Update a tenant registration
            </summary>
            <param name="id"></param>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantRegistrationController.Delete(System.Guid)">
            <summary>
             Delete a tenant registration
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantSchoolController.GetById(System.Guid)">
            <summary>
            Lấy thông tin trường học theo ID
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantSchoolController.Create(Hoclieu.Core.Dtos.Tenant.TenantSchoolDto)">
            <summary>
            Tạo mới trường học
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantSchoolController.Update(System.Guid,Hoclieu.Core.Dtos.Tenant.TenantSchoolDto)">
            <summary>
            Cập nhật thông tin trường học
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantSchoolController.Delete(System.Guid)">
            <summary>
            Xóa trường học
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantSchoolController.GetByTenantId(System.Int64)">
            <summary>
            Lấy thông tin trường học theo Tenant ID
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantStudentController">
             <summary>
            
             </summary>
             <remarks>
            
             </remarks>
             <param name="dbContext"></param>
             <param name="tenancyService"></param>
             <param name="tenancyUserManager"></param>
             <param name="classroomStudentRepository"></param>
             <param name="classroomTeacherRepository"></param>
             <param name="tenantUserService"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantStudentController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.User.TenancyService,Hoclieu.Services.User.TenancyUserManager,Hoclieu.Classrooms.ClassroomStudentRepository,Hoclieu.Classrooms.ClassroomTeacherRepository,Hoclieu.Services.User.TenantUserService)">
             <summary>
            
             </summary>
             <remarks>
            
             </remarks>
             <param name="dbContext"></param>
             <param name="tenancyService"></param>
             <param name="tenancyUserManager"></param>
             <param name="classroomStudentRepository"></param>
             <param name="classroomTeacherRepository"></param>
             <param name="tenantUserService"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantStudentController.GetList(Hoclieu.Core.Dtos.Tenant.GetListTenantStudentRequest)">
            <summary>
            Lấy danh sách học sinh trong tenant
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantStudentController.GetById(System.Int64)">
            <summary>
            Lấy ra chi tiết hồ sơ học sinh trong tenant
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantStudentController.Upsert(System.Int64,Hoclieu.Core.Dtos.Tenant.UpdateStudentInfoRequest)">
            <summary>
            Thêm hoặc cập nhật thông tin học sinh trong tenant.
            </summary>
            <param name="id">Id của học sinh trong tenant.</param>
            <param name="request">Thông tin cập nhật học sinh.</param>
            <returns>Kết quả thực hiện thêm hoặc cập nhật học sinh.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantStudentController.GetParent(System.Int64)">
            <summary>
            Lấy thông tin phụ huynh trong tenant
            </summary>
            <param name="tenantUserId">TenantUserId</param>
            <returns></returns>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantStudentController.AddRange(Hoclieu.Core.Dtos.Tenant.ImportStudentInfoRequest)">
            <summary>
            Thêm học sinh từ excel
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantTeacherController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantTeacherController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Microsoft.AspNetCore.Identity.RoleManager{Hoclieu.Domain.User.ApplicationRole},Hoclieu.Services.User.TenancyService,Hoclieu.Services.User.TenancyUserManager,Hoclieu.Classrooms.ClassroomTeacherRepository,Hoclieu.Services.User.TenantUserService)">
             <summary>
            
             </summary>
             <param name="dbContext"></param>
             <param name="userManager"></param>
             <param name="roleManager"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantTeacherController.GetById(System.Int64)">
            <summary>
            Lấy ra chi tiết hồ sơ học sinh trong tenant
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantTeacherController.ImportTeachers(Hoclieu.Core.Dtos.Tenant.ImportExcelRequest)">
            <summary>
            Import teachers from excel file
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.User.TenancyService,Hoclieu.Services.User.TenancyUserManager,Hoclieu.Classrooms.ClassroomStudentRepository,Hoclieu.Classrooms.ClassroomTeacherRepository)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController.GetAll">
            <summary>
            Get all tenant users
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController.Get(System.Int64)">
            <summary>
            Get a tenant user by id
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController.Create(Hoclieu.Core.Dtos.Tenant.TenantUserDto)">
            <summary>
            Create a new tenant user
            </summary>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController.Update(System.Int64,Hoclieu.Core.Dtos.Tenant.TenantUserDto)">
            <summary>
            Update a tenant user
            </summary>
            <param name="id"></param>
            <param name="dto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController.Delete(System.Guid)">
            <summary>
            Delete a tenant user
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.TenantUserController.GetStatistics">
            <summary>
            Thống kê số lượng học sinh
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.Tenant.WorkHistoryController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.WorkHistoryController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
             <summary>
            
             </summary>
             <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.WorkHistoryController.Create(Hoclieu.Core.Dtos.WorkHistory.WorkHistoryDto)">
            <summary>
            Creates a new work history entry.
            </summary>
            <param name="dto">The work history data to create.</param>
            <returns>Ok with the created work history data.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.WorkHistoryController.Update(System.Guid,Hoclieu.Core.Dtos.WorkHistory.WorkHistoryDto)">
            <summary>
            Updates an existing work history entry by its unique identifier.
            </summary>
            <param name="id">The unique identifier of the work history entry to update.</param>
            <param name="dto">The updated work history data.</param>
            <returns>Ok with the updated data if successful; NotFound if the entry does not exist.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Tenant.WorkHistoryController.Delete(System.Guid)">
            <summary>
            Deletes a work history entry by its unique identifier.
            </summary>
            <param name="id">The unique identifier of the work history entry to delete.</param>
            <returns>No content if deleted successfully; NotFound if the entry does not exist.</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.QuestionSetsController">
            <summary>
            QuestionBanksController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.QuestionSetsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Users.TeacherRepository,Hoclieu.Services.OpenAI.OpenAIService,Hoclieu.Services.SkillService)">
            <summary>
            QuestionBanksController constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBankFeedbacksController.GetAll(System.Int32,System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{Hoclieu.Core.Enums.Emotion})">
            <summary>
            Lấy danh sách các feedback
            </summary>
            <param name="skipcount"></param>
            <param name="maxResultCount"></param>
            <param name="fromDate"></param>
            <param name="toDate"></param>
            <param name="emotion"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBankFeedbacksController.AddFeedback(Hoclieu.Core.Dtos.CreateTestFeedbackRequest)">
            <summary>
            Thêm phản hồi
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBankFeedbacksController.DeleteFeedback(System.Guid)">
            <summary>
            Xóa phản hồi 
            </summary>
            <param name="Id"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetDetailBeShareUserBySkillId(System.Guid,Hoclieu.Core.Dtos.TestBank.GetDetailBeShareUserBySkillId)">
            <summary>
            API xem chi tiết danh sách những người đã chia sẻ đề
            đầu vào: định danh đề
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetAllTeacherShareTest(System.Guid,System.Guid)">
            <summary>
            Lấy danh sách người dùng đã chia sẻ đề cho giáo viên (Dùng cho bộ lọc)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetAllTeacherBeShareTest(System.Guid,System.Guid)">
            <summary>
            Lấy danh sách người dùng mà mình đã chia sẻ đề cho họ (Dùng cho bộ lọc)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.AddCheckpointToTestBank(Hoclieu.Core.Dtos.TestBank.AddCheckpointToTestBankReuest)">
            <summary>
            Thêm đề thi vào ngân hàng đề
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.UpdateViewStatusOfTestBank(System.Guid,Hoclieu.Core.Enums.ViewStatus)">
            <summary>
            Update view mode of TestBank
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.UpdateViewStatusOfTest(System.Guid,Hoclieu.Core.Enums.ViewStatus)">
            <summary>
            Update view mode of TestBankCheckpoint
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.Delete(System.Guid)">
            <summary>
            Delete TestBank
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.DeleteCheckpointFromTestBank(Hoclieu.Core.Dtos.TestBank.AddCheckpointToTestBankReuest)">
            <summary>
            Delete TestBankCheckpoint
            </summary>
            <param name="request">Định danh kỹ năng</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.CreateTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.TestBankTeacherCustomCreateDto)">
            <summary>
            Tạo đề test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetQuestionCanBeUsedForTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.GetQuestionCanBeUsedForTestBankTeacherCustomRequest)">
            <summary>
             Get question can be used for test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetGroupQuestion(Hoclieu.Core.Dtos.TestBank.GetGroupQuestionRequest)">
            <summary>
            Lấy dữ liệu nhóm câu hỏi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.SaveTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.SaveTestBankTeacherCustomRequest)">
            <summary>
            save test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetMatrixTestBankTeacherCustom(System.Guid)">
            <summary>
            Get matrix test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.SaveQuestionMatrixTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.SaveQuestionMatrixTestBankTeacherCustomRequest)">
            <summary>
            Save question when change question in matrix test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.SaveGroupQuestionMatrixTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.SaveQuestionGroupMatrixTestBankTeacherCustomRequest)">
            <summary>
            Save group question when change question in matrix test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.DownloadMatrixTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.MongoMatrixCustomRequest)">
            <sumary>
            Matrix download in mongo
            </sumary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.DeleteMatrixTestBankTeacherCustom(System.Guid)">
            <summary>
            delete MongoMatrixCustomRequest
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetMatrixTestBankTeacherCustomBySkillId(System.Guid)">
            <summary>
            Get MongoMatrixCustomRequest by skillId
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.ChangeNumericalQuestionMatrixTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.UpdateQuestionOrderRequest)">
            <summary>
            Change question numericalOrder in matrix test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.ChangeNumericalKnowledgeSkillMatrixTestBankTeacherCustom(Hoclieu.Core.Dtos.TestBank.UpdateKnowledgeSkillOrderRequest)">
            <summary>
            Change knowledgeSkill numericalOrder in matrix test bank teacher custom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetTestBankInformation(System.Guid)">
            <summary>
            Get Testbank Custom Information
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.GetTestBankDataByCheckpointCacheId(System.Guid)">
            <summary>
            Get Testbank Custom Information
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.CopyTestBankCustomExam(System.Guid)">
            <summary>
            Copy test-bank-custom-exam
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanksController.ReplaceKnowledgeSkills(Hoclieu.Core.Dtos.TestBank.ReplaceKnowledgeSkillsDto)">
            <summary>
            Update knowledgeSkills from one test-bank-custom-exam to another
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.TestDownloadHistorysController">
            <summary>
            TestDownloadHistorysController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestDownloadHistorysController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.MongoCheckpointCacheInfoRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheRepository)">
            <summary>
            TestDownloadHistorysController constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestDownloadHistorysController.GetAllMyTestDownloadHistory(Hoclieu.Core.Dtos.TestBank.GetAllMyTestDownloadHistoryRequest)">
            <summary>
            Lấy danh sách TestDownloadHistory
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestDownloadHistorysController.CreateTestDownloadHistory(Hoclieu.Core.Dtos.TestBank.CreateTestDownloadHistoryRequest)">
            <summary>
            Tạo mới TestDownloadHistory
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestDownloadHistorysController.DeleteCreateTestDownloadHistory(System.Guid)">
            <summary>
            Delete TestDownloadHistory by checkpointCacheId
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.TestsController">
            <summary>
            TestDownloadHistorysController
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetQuestionTest(System.Guid,System.Nullable{System.Int32},Hoclieu.Core.Enums.ClientType)">
            <summary>
            API tạo đề của kỹ năng checkpoint
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetCheckpointSampleDetail(System.Guid,System.Nullable{Hoclieu.Core.Enums.CheckpointCacheType})">
            <summary>
            API lấy thông tin đề thừ checkpointQuestionCache
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.DeleteCheckpointCache(System.Guid)">
            <summary>
            Xóa đề được tạo ra mà chưa hoàn thành từ đề động và các bản nháp của nó
            </summary>
            <param name="id">Định danh kỹ năng đề động</param>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.MakeDraftCheckpointCacheBySkillIdAsync(System.Guid,Hoclieu.Core.Dtos.TestBank.MakeDraftCheckpointCacheByCheckpointRequest,Hoclieu.Core.Enums.ClientType,Hoclieu.Core.Enums.CheckpointCacheType)">
            <summary>
            Tạo bản nháp từ đề động
            </summary>
            <param name="checkpointCacheId"></param>
            <param name="status"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetCheckpointSampleDetailPublic(System.Guid)">
            <summary>
            API lấy thông tin đề thừ checkpointQuestionCache
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetCheckpointCacheNearest(System.Guid,System.Boolean,System.Boolean)">
            <summary>
            Lấy danh sách các đề tĩnh đã được tạo từ đề động nếu có đề đã lưu thì lấy luôn đề lưu gần nhất hoặc lấy một đề mới và lưu lại
            </summary>
            <param name="checkpointId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetSuggestionInfo(System.Guid,Hoclieu.Core.Enums.ClientType)">
            <summary>
            API lấy câu hỏi của đề kiểm tra đc giao
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetSkillExamSuggestionShareURLForStudent(System.Guid,Hoclieu.Core.Enums.ClientType)">
            <summary>
            Share url cho học sinh
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetQuestionHistory(System.Guid)">
            <summary>
            API lấy kết quả của đề kiểm tra đc giao
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetHistoryTestBanks(System.Guid)">
            <summary>
            View history of exam
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetCheckPointDetailQuestionData(System.Guid)">
            <summary>
            Get question data for checkpoint detail
            </summary>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetCheckPointDetailQuestionsData(Hoclieu.Core.Dtos.TestBank.UpdateCheckpointDetailsRequest)">
            <summary>
            Get question data for checkpoint detail
            </summary>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetQuestionByCheckpointQuestionCacheId(System.Guid,Hoclieu.Checkpoints.GetCheckpointQuestionRequests)">
            <summary>
            Tìm kiếm câu hỏi để gán vào đề mẫu
            </summary>
            <param name="checkpointQuestionCacheId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.UpdateCheckpointQuestionCacheFromQuestionBank(System.Guid,Hoclieu.Core.Dtos.TestBank.QuestionBankDto)">
            <summary>
            Cập nhật câu hỏi trong đề mẫu tự bộ câu hỏi của tôi
            </summary>
            <param name="checkpointQuestionCacheId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.UpdateCheckpointQuestionCacheFromQuestionBeGeneratedByAI(System.Guid,Hoclieu.Skills.AddQuestionRequest,System.Nullable{System.Guid},System.Boolean)">
            <summary>
            Cập nhật câu hỏi trong đề mẫu từ câu hỏi tạo bằng AI
            </summary>
            <param name="checkpointQuestionCacheId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.UpdateCheckpointQuestionCacheFromQuestionBeGeneratedByAI(System.Int32,Hoclieu.Skills.AddQuestionRequestV2)">
            <summary>
            Cập nhật câu hỏi trong đề mẫu từ câu hỏi tạo bằng AI
            </summary>
            <param name="headerCustomKnowledgeSkillId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.UpdateContentQuestionFromAI(System.Int32,System.Collections.Generic.List{Hoclieu.Skills.AddQuestionRequestV2})">
            <summary>
            Cập nhật câu hỏi trong đề mẫu từ câu hỏi tạo bằng AI
            </summary>
            <param name="headerCustomKnowledgeSkillId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.RemoveCache(System.Guid)">
            <summary>
            Xoá cache của đề
            </summary>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestsController.GetQuestionBeUsedByUser(System.Guid,System.Nullable{System.Guid},System.Int32)">
            <summary>
            Lấy câu hỏi được sủ dụng nhiều để thay thế
            </summary>
            <param name="checkpointQuestionCacheId"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestShareHistoriesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheInfoRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheRepository)">
            <summary>
            TestShareHistoriesController constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestShareHistoriesController.GetMyTestBankBeShare(Hoclieu.Core.Dtos.TestBank.GetAllTestBeShareRequest)">
            <summary>
            Lấy danh sách đề thi được chia sẻ của giáo viên
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestShareHistoriesController.GetMyTestBankShare(Hoclieu.Core.Dtos.TestBank.GetAllTesShareRequest)">
            <summary>
            Lấy danh sách đề thi đã chia sẻ của giáo viên
            </summary>
            <param name="request"></param>
            <returns>Danh sách các đề đã chia sẻ</returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TestBanks.TestSuggestionsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.MongoCheckpointCacheInfoRepository,Hoclieu.Mongo.Service.MongoCheckpointCacheRepository,Hoclieu.Mongo.Service.MongoCheckpointQuestionCacheRepository)">
            <summary>
            TestShareHistoriesController constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TimeTable.TimeTableController.Create(Hoclieu.Core.Dtos.Facilities.TimeTableRequest)">
            <summary>
            Tạo thời khoá biểu
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TimeTable.TimeTableController.CreateMany(System.Collections.Generic.List{Hoclieu.Core.Dtos.Facilities.TimeTableRequest})">
            <summary>
            Tạo nhiều thời khoá biểu
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TimeTable.TimeTableController.Get(System.Guid,System.DateTime,System.DateTime)">
            <summary>
            Lấy danh sách
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TimeTable.TimeTableController.Put(System.Int32,Hoclieu.Core.Dtos.Facilities.TimeTableRequest)">
            <summary>
            Chỉnh sửa thời khoá biểu
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TimeTable.TimeTableController.Delete(System.Int32)">
            <summary>
            Xoá
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TimeTable.TimeTableController.GetById(System.Int32)">
            <summary>
            lấy thời khoá biểu theo id
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlossaryController.GetAll(System.String,System.Nullable{System.Int32},System.Int32,System.Int32,System.Boolean)">
            <summary>
            get data
            </summary>
            <param name="words"></param>
            <param name="level"></param>
            <param name="skipCount"></param>
            <param name="maxResultCount"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlossaryController.Create(Hoclieu.Core.Dtos.Dictionary.DictionaryDto)">
            <summary>
            create
            </summary>
            <param name="dictionaryDto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlossaryController.Update(System.Guid,Hoclieu.Core.Dtos.Dictionary.DictionaryDto)">
            <summary>
            update by id
            </summary>
            <param name="id"></param>
             <param name="dictionaryDto"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlossaryController.Delete(System.Guid)">
            <summary>
            delete by id
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.GlossaryWordTypeController.Delete(System.Guid)">
            <summary>
            Xoá từ loại glossory
            </summary>
            <param name="id">Định danh bản ghi</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.Import(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            import file txt
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.Search(System.String,System.Nullable{System.Guid},System.Nullable{System.Guid},System.Nullable{System.Guid},System.Nullable{System.Guid})">
            <summary>
            SEARCH WORD
            </summary>
            <param name="word"></param>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.SearchGoogle(System.String)">
            <summary>
            Dịch từ với google
            </summary>
            <param name="word"></param>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.GetAll(System.String,System.Int32,System.Int32,Hoclieu.Core.Enums.Dictionary.SearchType)">
            <summary>
            get all word
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.Edit(System.Guid,Hoclieu.Core.Dtos.Dictionary.DictionaryDto)">
            <summary>
            edit by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.Create(Hoclieu.Core.Dtos.Dictionary.DictionaryDto)">
            <summary>
            create new word
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.Creates(Hoclieu.Core.Dtos.Dictionary.CreateGlossaryWordsRequest)">
            <summary>
            create new word
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.Delete(System.Guid)">
            <summary>
            delete by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.GetById(System.Guid)">
            <summary>
            get word by id
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.UpdateWordNomalizeDictionaries(Hoclieu.Core.Dtos.UpdateDictionariesByIdsRequest)">
            <summary>
             api to update wordNomalize of Dictionary
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.TranslateController.UpdateWordNomalizeGlossaries(Hoclieu.Core.Dtos.UpdateDictionariesByIdsRequest)">
            <summary>
             api to update wordNomalize of Dictionary
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.WordTypeController.Create(Hoclieu.Core.Dtos.Dictionary.AddWordTypeRequest)">
            <summary>
            Thêm từ loại
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.WordTypeController.Update(System.Guid,Hoclieu.Core.Dtos.Dictionary.AddWordTypeRequest)">
            <summary>
            Cập nhật từ loại
            </summary>
            <param name="id">Định danh loại từ</param>
            <param name="request">Dữ liệu cập nhật</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.AccountAITempsController.AddAccountAI(System.String)">
            <summary>
            Add new account AI Temp
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.AccountAITempsController.GetAllAccountAI">
            <summary>
            Get All Account AI Temp
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.AccountAITempsController.DeleteAccountAI(System.Int32)">
            <summary>
             delete account AI Temp
             </summary>
             <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController">
            <summary>
            Xác minh giáp viên
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController.#ctor(Hoclieu.EntityFrameworkCore.User.TeacherVerificationRepository,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.Extensions.Options.IOptions{Hoclieu.Settings.AppSettings},Hoclieu.Services.FileService,Hoclieu.Services.NotificationService)">
            <summary>
            Hàm khởi tạo
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController.GetDetail(System.Guid)">
            <summary>
            Lấy chi tiết yêu cầu Xác minh
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController.GetTeacherVerifications(System.Int32,System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Nullable{Hoclieu.Core.Enums.User.TeacherVerificationStatus},System.Nullable{Hoclieu.Core.Enums.User.TeacherVerificationType},System.Nullable{System.Guid})">
            <summary>
            API lấy thông tin yêu cầu xác minh của giáo viên
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController.Add(Hoclieu.Core.Dtos.User.TeacherVerificationRequest)">
            <summary>
            Gửi yêu cầu xác minh giáo viên
            </summary>
            <param name="request">Dữ liệu yêu cầu</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController.UpdateTeacherVerification(System.Guid,Hoclieu.Core.Enums.User.TeacherVerificationStatus,System.String)">
            <summary>
            Cập nhật trạng thái xác minh giáo viên
            </summary>
            <param name="id">ĐỊnh danh yêu cầu</param>
            <param name="status">Trạng thái sau thay đổi</param>
            <param name="reason">Lý do thay dổi trạng thái</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController.CheckTeacherVerified">
            <summary>
            API check teacher is upgraded by system
            </summary>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.User.TeacherVerificationsController.UpdateSubjectTeacherVerify(System.Nullable{System.Guid})">
            <summary>
            Cap nhat thong tin mon hoc cho tai khoan gv
            </summary>
            <param name="subjectId"></param>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.AgentManagersController">
            <summary>
            Quản lý đại lý
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.#ctor(Hoclieu.Services.UserService,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Hoclieu.Services.AgentManagersService)">
            <summary>
            Hàm khởi tạo controller agent manager
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.CreateAgentManager(System.Collections.Generic.List{Hoclieu.Users.CreateUserRequest},System.String)">
            <summary>
            Tạo đại lý
            </summary>
            <returns>Thông tin đại lý</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.GetMyAgent">
            <summary>
            Lấy thông tin đại lý
            </summary>
            <returns>Thông tin đại lý</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.GetAgentChildren(System.Guid)">
            <summary>
            Lấy thông tin các đại lý con
            </summary>
            <param name = "id" >Id đại lý</param>
            <returns>Thông tin các đại lý con</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.CreateAgentPayment(Hoclieu.AgentManagers.CreateAgentPaymentRequest)">
            <summary>
            Tạo xác minh thanh toán
            </summary>
            <param name = "request" >Dữ liệu thanh toán</param>
            <returns>Thông tin thanh toán</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.FindAgentPayments(Hoclieu.AgentManagers.FindAgentPaymentsRequest)">
            <summary>
            Tìm kiếm xác minh thanh toán
            </summary>
            <param name = "request" >Dữ liệu tìm kiếm</param>
            <returns>Thông tin các thanh toán</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.ConfirmAgentPayment(System.Guid,Hoclieu.AgentManagers.ConfirmAgentPaymentRequest)">
            <summary>
            Tìm kiếm xác minh thanh toán
            </summary>
            <param name = "id" >Id xác minh thanh toán</param>
            <param name = "request" >Dữ liệu cập nhật</param>
            <returns>Thông tin các thanh toán</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.DistributeCard(Hoclieu.AgentManagers.DistributeCardRequest)">
            <summary>
            Phân phối thẻ
            </summary>
            <param name = "request" >Dữ liệu chia sẻ</param>
            <returns>Thông tin phân phối</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.FindDistributionCardChanges(Hoclieu.AgentManagers.FindDistributionCardChangesRequest)">
            <summary>
            Tìm kiếm thay đổi thẻ
            </summary>
            <param name = "request" >Dữ liệu tìm kiếm</param>
            <returns>Danh sách thẻ</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.FindUserOfAgent(Hoclieu.AgentManagers.FindUserOfAgentRequest)">
            <summary>
            API lấy ra các tài khoản được tạo bỏi đại lý
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.AgentManagersController.ActivateCard(Hoclieu.AgentManagers.AgentActivateCardForAnotherUserRequest)">
            <summary>
            Kích hoạt hộ cổng luyện thi
            </summary>
            <param name = "request" >Dữ liệu kích hoạt</param>
            <returns>Thông tin kích hoạt</returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController">
             <summary>
            
             </summary>
            
            
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy">
            <summary>
            enum for type of group by
            </summary>
        </member>
        <member name="F:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy.All">
            <summary>
            group by all
            </summary>
        </member>
        <member name="F:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy.Type">
            <summary>
            group by log type
            </summary>
        </member>
        <member name="F:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy.BookType">
            <summary>
            group by book type
            </summary>
        </member>
        <member name="F:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy.Subject">
            <summary>
            group by subject
            </summary>
        </member>
        <member name="F:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy.TypeAndBookType">
            <summary>
            group by log type and book type
            </summary>
        </member>
        <member name="F:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy.TypeAndSubject">
            <summary>
            group by log type and subject
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
             <summary>
            
             </summary>
             <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.AddUserChangeModeScreenLog(Hoclieu.Core.Dtos.User.UserChangeModeScreenLogDto)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GetUserChangeModeScreenLog(Hoclieu.HttpApi.Host.Controllers.UserChangeModeScreenLogsController.GroupBy)">
            <summary>
            thống kê số lần thay đổi chế độ màn hình
            </summary>
            <param name="groupBy">
            </param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserFeedbackController.GetAllFeedBack(Hoclieu.UserFeedbacks.GetUserFeedbackRequest)">
            <summary>
            API lấy tất cả user feedback thỏa mãn.
            </summary>
            <param name="request"></param>
            <returns>List items và số lượng phần tử của list.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserFeedbackController.GetAllNoteFeedback(System.Int32)">
            <summary>
            API lấy tất cả comment của user feedback theo Id.
            </summary>
            <param name="feedbackId"></param>
            <returns>List items và số lượng phần tử của list.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserFeedbackController.AddFeedback(Hoclieu.UserFeedbacks.UserFeedbackRequest)">
            <summary>
            API tạo mới user feedback.
            </summary>
            <param name="request"></param>
            <returns>id của User Feedback đó.</returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserFeedbackController.AddFeedbackComment(System.Int32,Hoclieu.UserFeedbacks.FeedbackCommentRequest)">
            <summary>
            API tạo mới commnet cho user feedback theo Id.
            </summary>
            <param name="feedbackId"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserFeedbackController.UpdateFeedback(System.Int32,Hoclieu.Core.Enums.UserFeedbackStatus,Hoclieu.Core.Enums.UserFeedBackType)">
            <summary>
            API cho phép cập nhật user feedback.
            </summary>
            <param name="feedbackId"></param>
            <param name="status"></param>
            <param name="type"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserFeedbackController.ExportUserFeedback(Hoclieu.UserFeedbacks.GetUserFeedbackRequest)">
            <summary>
            API xuất dữ liệu userFeeback ra google sheet
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.UserImportsController">
            <summary>
            Controller for user import
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Microsoft.AspNetCore.Identity.UserManager{Hoclieu.Users.ApplicationUser},Hoclieu.Services.ClassroomService)">
            <summary>
            constructor
            </summary>
            <param name="dbContext"></param>
            <param name="userManager"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.ImportData(Hoclieu.Core.Dtos.User.ImportUserRequest)">
            <summary>
            import data from gg sheet
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.Get(Hoclieu.Dtos.PagedAndSortedResultRequest,Hoclieu.Users.StatusUserImport,System.String,System.String)">
            <summary>
            Get all UserImport
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.GetUserImportBySchoolAndStatus(System.Guid,Hoclieu.Users.StatusImport)">
            <summary>
            Get user imported by school
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.GetSchoolInfoById(System.Guid)">
            <summary>
            Get user imported by school
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.Delete(Hoclieu.Users.StatusImport)">
            <summary>
            delete UserImport
            </summary>
            <param name="statusImport"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.CreateUsername(System.Guid)">
            <summary>
            create Username
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.CreateUser(System.Guid,System.Int32)">
            <summary>
            create User
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.CreateClassroom(System.Guid)">
            <summary>
            Create classroom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.AddStudentTeacher(System.Guid)">
            <summary>
            add student and teacher to classroom
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserImportsController.RakeCard(System.Guid,System.Int32)">
            <summary>
            Kích hoạt CLT,...
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Controllers.UserResourcesController">
            <summary>
            Quản lý tài nguyên người dùng
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Services.UserResourseService,Hoclieu.Services.NotificationService,Hoclieu.Services.EmailService,Hoclieu.EmailCheckers.EmailCheckerService)">
            <summary>
            Hàm khởi tạo controller user resource
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.GetUserResource">
            <summary>
            Lấy thông tin số lượng thẻ
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.ActivateCard(Hoclieu.UserResources.ActivateCardRequest)">
            <summary>
            Kích hoạt cổng luyện thi
            </summary>
            <param name = "request" >Dữ liệu kích hoạt</param>
            <returns>Thông tin kích hoạt</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.ReactivateUserGraduationCard(System.Guid,System.Guid,System.Guid)">
            <summary>
            API đổi môn sau khi kích hoạt CLT
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.ShareCard(Hoclieu.UserResources.ShareCardRequest)">
            <summary>
            Chia sẻ thẻ kích hoạt
            </summary>
            <param name = "request" >Dữ liệu chia sẻ</param>
            <returns>Thông tin chia sẻ</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.FindCardChanges(Hoclieu.UserResources.FindCardChangesRequest)">
            <summary>
            Tìm kiếm thay đổi thẻ
            </summary>
            <param name = "request" >Dữ liệu tìm kiếm</param>
            <returns>Danh sách thẻ</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.FindUserCardChanges(System.String,Hoclieu.UserResources.FindCardChangesRequest)">
            <summary>
            Tìm kiếm thay đổi thẻ người dùng
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.CreateUserResourceOrderAsync(Hoclieu.UserResources.CreateUserResourceOrderRequest)">
            <summary>
            Tạo đơn hàng số lượng lớn
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.ExchangeShareCard(Hoclieu.UserResources.ExchangeReferralCardRequest)">
            <summary>
            Quy đổi thẻ chia sẻ sang thẻ kích hoạt
            </summary>
            <param name="request"></param>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.FindOrderBulk(Hoclieu.UserResources.FindOrderBulksRequest)" -->
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.FindMyOrderBulk(Hoclieu.Dtos.PagedAndSortedResultRequest)" -->
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.UpdateOrderBulk(System.Guid,Hoclieu.UserResources.UpdateOrderBulksRequest)">
            <summary>
            Update trạng thái đơn hàng số lượng lớn
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.GetUserCheckpoint(System.String,Hoclieu.Dtos.PagedAndSortedResultRequest)">
            <summary>
            Lấy danh sách lịch sửa mua CLT
            </summary>
            <param name="userName">Tên người dùng</param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UserResourcesController.UpdateAccountTeacherOrStudentUserGraduationBySchoolId(System.Guid)">
            <summary>
            update account in school
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.Get(Hoclieu.Users.GetUsersRequest)">
            <summary>
            lấy danh sách người dùng dành cho super admin
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.UpdateUser(System.String,Hoclieu.Users.UpdateUserRequest)">
            <summary>
            Cập nhật thông tin người dùng
            </summary>
            <param name = "userName" >Tên đăng nhập</param>
            <param name = "request" >Dữ liệu cập nhật</param>
            <returns>Thông tin người dùng</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.CreateEditorGradeSubjectCategory(Hoclieu.Users.CreateEditorGradeSubjectCategoryRequest)">
            <summary>
            gán quyền thư mục cho người dùng
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.GetMyProfile">
            <summary>
             Lấy thông tin người dùng hiện tại
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.UpdateMyProfile(Hoclieu.Users.UpdateProfileRequest)">
            <summary>
            Cập nhật thông tin cá nhân của người dùng hiện tại
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.CheckFollow">
            <summary>
            kiểm tra người dùng đã follow Officail Account Zalo của hệ thống hay chưa
            </summary>
            <returns>Giá trị true hoặc false</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.UpdateRoleEmailConfirmStatus(Hoclieu.Users.UpdateRoleActivationStatusRequest)">
            <summary>
            API cập nhật trạng thái xac thực email của user
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.UpdateRoleActivationStatus(Hoclieu.Users.UpdateRoleActivationStatusRequest)">
            <summary>
            API cập nhật trạng thái kích hoạt của các role
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.CreateAcountForTeacher(System.Collections.Generic.List{Hoclieu.Users.CreateTeacherRequest})">
            <summary>
            Tao moi tai khoan cho giao vien hang loat
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.ResetPassword(System.String)">
            <summary>
            API đặt lại mật khẩu người dùng
            </summary>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.SuggestionEmail(System.String)">
             <summary>
            
             </summary>
             <param name="userName"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.SwitchRole(System.String)">
            <summary>
            switch beetwen user role student and teacher
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.SendOtpEmail(Hoclieu.Core.Dtos.Auth.VeriyEmailRequest)">
            <summary>
            Send OTP email for verify email
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.SendOtpSms(Hoclieu.Core.Dtos.Auth.VerifyPhoneNumberRequest)">
            <summary>
            Gửi mã OTP xác thực số điện thoại
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.VerifyOptEmail(Hoclieu.Core.Dtos.Auth.VerifyEmailOrPhoneNumberRequest)">
            <summary>
            Xác thực mã OTP email
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.UsersController.VerifyOptSms(Hoclieu.Core.Dtos.Auth.VerifyEmailOrPhoneNumberRequest)">
            <summary>
            Xác thực mã OTP số điện thoại
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheeResultController.DoneMark(Hoclieu.Skills.MarkDoneRequest)">
            <summary>
            DoneMark
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheeResultController.DoneMarkMultiWorksheet(Hoclieu.Skills.MarkDoneMultiRequest)">
            <summary>
             Xác nhận chấm xong cho nhiều lớp
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheeResultController.SkipMarkMulti(Hoclieu.Skills.MarkDoneMultiRequest)">
            <summary>
            Bỏ chấm nhiều lớp
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheeResultController.SkipMark(Hoclieu.Skills.MarkSkipRequest)">
            <summary>
            SkipMark
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetListSubject">
            <summary>
            Get subjects
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetListGrade(System.Guid)">
            <summary>
            Get grade by grade
            </summary>
            <param name="subjectId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetListWorksheet(Hoclieu.Core.Dtos.Worksheet.PaginationWorkSheetRequest)">
            <summary>
            Lấy danh sách phiếu bài tập
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.CreateWorksheet(Hoclieu.Core.Dtos.Worksheet.WorksheetRequest)">
            <summary>
            Create new worksheet
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.DeleteWorksheet(System.Guid)">
            <summary>
            Delete worksheet
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.UpdateQuestion(Hoclieu.Core.Dtos.Worksheet.UpdateQuestionWorksheet,System.Guid)">
            <summary>
            Update question to worksheet
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetForEdit(System.Guid,System.Boolean)">
            <summary>
            Lấy thông tin phiếu bài tập để chỉnh sửa
            </summary>
            <param name="worksheetId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetDetail(System.Guid)">
            <summary>
            Get worksheet detail
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetDetailPrint(System.Guid)">
            <summary>
            Get worksheet detail
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetAnswer(System.Guid)">
            <summary>
            Giáo viên xem phiếu bài tập hoặc học sinh phiếu bài tập
            </summary>
            <param name="suggestionDataId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.SaveWorksheetAnswer(Hoclieu.Core.Dtos.Worksheet.WorksheetAnswerRequest)">
            <summary>
            save từ câu hỏi của bài tập
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.SubmitWorksheet(System.Guid)">
            <summary>
            Nộp bài
            </summary>
            <param name="worksheetId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetAssignment(Hoclieu.Core.Dtos.Worksheet.PaginationWorkSheetRequest)">
            <summary>
            Danh sách giao bài
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetListClassAssignedByWorksheet(System.Guid)">
            <summary>
            Lấy danh sách lớp được giao bài
            </summary>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetCreateByEditor(System.Guid)">
            <summary>
            Lấy danh sách phiếu bài tập do BTV tạo
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetApprove(Hoclieu.Core.Dtos.Worksheet.PaginationWorkSheetRequest)">
            <summary>
            Danh sách phiếu duyệt
            </summary>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.ChangeStatusWorksheet(System.Guid)" -->
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.PractiseWorksheetInfo(System.Guid)" -->
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.SaveWorksheetAnswerPractise(Hoclieu.Core.Dtos.Worksheet.WorksheetAnswerPractiseRequest)">
            <summary>
            save từ câu hỏi của bài tập (luyện tập)
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.SubmitWorksheetPractise(System.Guid)">
            <summary>
            Nộp bài (luyện tập)
            </summary>
            <param name="worksheetResultId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetWorksheetDetailBySkill(System.Guid)">
            <summary>
            Get worksheet detail
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.SaveTimeDurationWorksheet(System.Guid,Hoclieu.Core.Dtos.Worksheet.UpdateTime)">
            <summary>
            Save thời gian khi học sinh thoát worksheet (luyên tâp)
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetAllTree(Hoclieu.Categories.GetCategoryAllTreeRequest)">
            <summary>
            Retrieves all category trees based on the provided request parameters.
            </summary>
            <param name="request">The request containing filtering parameters.</param>
            <returns>A list of category DTOs representing the tree structure.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.ListWorksheetApprove(Hoclieu.Core.Dtos.Worksheet.WorksheetListApprove)">
            <summary>
            Approve or UnApprove list
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetSubjectByGradeInWorksheet">
            <summary>
            Lấy danh sách lớp môn
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetController.GetQuestionSkillBySkillTemplateDataId(System.Guid)">
            <summary>
            Retrieves the question skill associated with the specified skill template data ID.
            </summary>
            <param name="skillTemplateDataId">The unique identifier of the skill template data.</param>
            <returns>The question skill corresponding to the provided skill template data ID.</returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetShareController.ShareWorksheet(Hoclieu.Core.Dtos.Worksheet.WorksheetShareRequest)">
            <summary>
            Add share worksheet
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetShareController.GetListSharedWorksheet(Hoclieu.Core.Dtos.Worksheet.PaginationRequest)">
            <summary>
            Danh sách phiếu bài tập đã chia sẻ
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetShareController.RemoveSharedWorksheet(System.Guid)">
            <summary>
            Thu hồi chia sẻ phiếu bài tập
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetShareController.GetListSharedByMeWorksheet(Hoclieu.Core.Dtos.Worksheet.PaginationRequest)">
            <summary>
            Danh sách phiếu bài tập đã được chia sẻ
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetShareController.GetSharedWith(System.Guid,System.String)">
            <summary>
             Danh sách người được chia sẻ phiếu bài tập
            </summary>
            <param name="worksheetId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetShareController.DeleteWorksheet(System.Guid,System.Guid)">
            <summary>
            delete worksheet by worksheet and userid
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Controllers.Worksheet.WorksheetShareController.DeleteWorksheetWithMe(System.Guid)">
            <summary>
            Xoá phiêú bài đã chia sẻ với người dùng
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Helpers.AuthorizeAttribute">
            <summary>
            AuthorizeAttribute
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.AuthorizeAttribute.#ctor(System.String[])">
            <summary>
            AuthorizeAttribute constructor
            </summary>
            <param name="roles"></param>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.AuthorizeAttribute.OnAuthorization(Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)">
            <summary>
            Kiểm tra quyền truy cập
            </summary>
            <param name="context"></param>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Helpers.ContestAttribute">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.ContestAttribute.#ctor">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.ContestAttribute.OnAuthorization(Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)">
             <summary>
            
             </summary>
             <param name="context"></param>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Helpers.RedisConnectorHelper">
            <summary>
            Class RedisConnectorHelper
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.RedisConnectorHelper.#ctor(Microsoft.Extensions.Options.IOptions{Hoclieu.Services.Settings.ConnectionStrings})">
            <summary>
            Contructor RedisConnectorHelper
            </summary>
            <param name="connectionStrings"></param>
        </member>
        <member name="P:Hoclieu.HttpApi.Host.Helpers.RedisConnectorHelper.Connection">
            <summary>
            Redis connection
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.RedisConnectorHelper.Dispose">
            <summary>
            Close connection
            </summary>
        </member>
        <member name="T:Hoclieu.HttpApi.Host.Helpers.TrialAttribute">
            <summary>
            TrialAttribute
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.TrialAttribute.#ctor">
            <summary>
            TrialAttribute constructor
            </summary>
        </member>
        <member name="M:Hoclieu.HttpApi.Host.Helpers.TrialAttribute.OnAuthorization(Microsoft.AspNetCore.Mvc.Filters.AuthorizationFilterContext)">
            <summary>
            Kiểm tra quyền truy cập
            </summary>
            <param name="context"></param>
        </member>
        <member name="M:Hoclieu.Controllers.CategoriesController.GetAllTree(Hoclieu.Categories.GetCategoryAllTreeRequest)">
            <summary>
            Get all tree categories
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.CategoriesController.GetQuestions(System.Collections.Generic.List{System.Guid},System.Int32,System.Boolean,System.Boolean,System.String)">
            <summary>
            Get all question of skills
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.CategoriesController.ExportNumberDataInCategory(System.Guid)">
            <summary>
            Get all number question of category
            </summary>
            <param name="categoryId"></param>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.Controllers.OnLuyenStreakController">
            <summary>
            Controller cho mục tiêu hàng ngày
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.OnLuyenStreakController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.OnLuyenStreaks.OnLuyenStreakRepository,Hoclieu.Mongo.Service.StudyLogsRepository,Microsoft.Extensions.Caching.Distributed.IDistributedCache)">
            <summary>
            Controller cho mục tiêu hàng ngày
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.DepartmentsController.GetSchools(System.Guid)">
            <summary>
            Lấy danh sách trường có trong sở giáo dục
            </summary>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SchoolsController.GetSchoolsInDistrict(System.Guid)">
            <summary>
            Lấy ra các trường trong huyện
            </summary>
            <param name="districtId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SchoolsController.CountRequests(System.Int32)">
            <summary>
            Đếm số lượng yêu cầu thêm lớp học vào trường.
            </summary>
            <returns>Số lượng yêu cầu.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SchoolsController.GetClassroomRequestsByFilter(Hoclieu.Schools.FilterRequestDto)">
            <summary>
            Lấy danh sách yêu cầu thêm lớp học vào trường theo bộ lọc.
            </summary>
            <param name="request">Bộ lọc yêu cầu.</param>
            <returns>Danh sách yêu cầu thêm lớp học vào trường.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SchoolsController.ConfirmSchoolTeacherStudent(System.Guid)">
            <summary>
            Xác nhận tất cả các tài khoản giáo viên và học sinh trong trường
            </summary>
            <param name="schoolId">Định danh trường</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="T:Hoclieu.Controllers.SkillCombineController">
            <summary>
            Class SkillCombineController.
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            constructor
            </summary>
            <param name="dbContext"></param>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.Get(System.Guid)">
            <summary>
            get skill combine by skill id\
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.CreateStage(Hoclieu.Domain.Skills.SkillCombineStageDto)">
            <summary>
            create SkillCombineStage
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.UpdateStage(Hoclieu.Domain.Skills.SkillCombineStageDto)">
            <summary>
            Update SkillCombineStage
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.DeleteStage(System.Guid)">
            <summary>
            Delete SkillCombineStage
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.CreateStageQuestion(Hoclieu.Domain.Skills.SkillCombineQuestionDto)">
            <summary>
            Create SkillCombineQuestion
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.CopyStageQuestion(System.Guid)">
            <summary>
            Copy SkillCombineQuestion a
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.UpdateStageQuestion(Hoclieu.Domain.Skills.SkillCombineQuestionDto)">
            <summary>
            Update SkillCombineQuestion
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.DeleteStageQuestion(System.Guid)">
            <summary>
            Delete SkillCombineQuestion
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.CreateStageSkill(Hoclieu.Domain.Skills.SkillCombineSkillDto)">
            <summary>
            Create SkillCombineSkill
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.DeleteStageSkill(System.Guid)">
            <summary>
            Delete SkillCombineSkill
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.CreateStageSkillTemplate(Hoclieu.Domain.Skills.SkillCombineTemplateDto)">
            <summary>
            Create SkillCombineTemplate
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillCombineController.DeleteStageSkillTemplate(System.Guid)">
            <summary>
            Delete SkillCombineTemplate
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillRelationshipsController.GetSkillRelationships(Hoclieu.Skills.GetSkillRelationshipsRequest)">
            <summary>
            Lấy danh sách quan hệ kỹ năng
            </summary>
            <param name="request">Dữ liệu lọc</param>
            <returns>Danh sách quan hệ kỹ năng</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillRelationshipsController.CreateSkillRelationship(Hoclieu.Skills.CreateSkillRelationship)">
            <summary>
            Tạo quan hệ kỹ năng
            </summary>
            <param name="request">Dữ liệu tạo</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillRelationshipsController.DeleteSkillRelationship(System.Guid)">
            <summary>
            Xoá quan hệ kỹ năng
            </summary>
            <param name="id">Id quan hệ kỹ năng</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillRelationshipsController.SortSkillRelationships(Hoclieu.Skills.SortSkillRelationshipsRequest)">
            <summary>
            Sắp xếp quan hệ kỹ năng
            </summary>
            <param name = "request" >Dữ liệu sắp xếp</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.Get(Hoclieu.Skills.GetSkillsRequest)">
            <summary>
            API lấy danh sách các skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetLessonTreeByLessonId(System.Guid,Hoclieu.Core.Enums.ClientType)">
            <summary>
            API lấy danh sách Skill theo LessonId
            </summary>
            <param name="lessonId"></param>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.Create(Hoclieu.Skills.CreateSkillRequest)">
            <summary>
            API tạo skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetById(System.Guid,Hoclieu.Core.Enums.ClientType)">
            <summary>
            API lấy thông tin chi tiết kỹ năng
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetCategoryBreadcrumbById(System.Guid)">
            <summary>
            API lấy thông tin đường dẫn của skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.Update(System.Guid,Hoclieu.Skills.UpdateSkillRequest)">
            <summary>
            API update skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.UpdateDataAsync(System.Guid,Hoclieu.Skills.UpdateDataSkillRequest)">
            <summary>
            API cập nhật data skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.UpdateQuestionAsync(Hoclieu.Skills.UpdateQuestionRequest)">
            <summary>
            Cập nhật ảnh trong câu hỏi tại ma trận kiến thức
            </summary>
            <param name="id"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.UpdateDataFromTemplate(System.Guid)">
            <summary>
            API cập nhật data skill từ tài liệu mô tả
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.MoveToCategory(System.Guid,Hoclieu.Skills.MoveToCategoryRequest)">
            <summary>
            API di chuyển thư mục trong ma trận kiến thức
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.Delete(System.Guid,System.Boolean)">
            <summary>
            API xoá skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetExample(System.Guid)">
            <summary>
            API lấy câu ví dụ
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetQuestion(System.Guid,System.Nullable{System.Guid},System.String,System.Guid,Hoclieu.Core.Enums.ClientType,Hoclieu.Core.Enums.SkillGetQuestionType,System.Boolean,System.Boolean,System.Int32)">
             <summary>
             API lấy câu hỏi các skill loại khác checkpoint
             </summary>
            
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.Answer(System.Guid,Hoclieu.Skills.AnswerRequest)">
            <summary>
            API kiểm tra đáp án
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.SaveAnswerEssay(System.Guid,Hoclieu.Skills.AnswerRequest)">
            <summary>
            API lưu đáp án câu tự luận
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetQuestionContentFromQuestionCache(System.Guid)">
            <summary>
            API lấy nội dung câu hỏi từ questionCacheId
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetAnswer(System.Guid,System.Guid)">
            <summary>
            API lấy đáp án đựa trên question cache
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.CheckAnswer(System.Guid,Hoclieu.Skills.AnswerRequest)">
            <summary>
            API kiểm tra đáp án người dùng
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.FindSkill(Hoclieu.Skills.FindSkillRequest)">
            <summary>
            API tìm skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetQuestionFromTemplateAndDataQuestion(Hoclieu.Skills.GetQuestionRequest)">
            <summary>
            API lấy thông tin câu hỏi
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetQuestionForAssignSkill(System.Guid)">
            <summary>
            Get preview questions for assign skill
            </summary>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetSkillsRecommendation(System.Guid)">
            <summary>
            Lấy danh sách kỹ năng gợi ý cho mỗi kỹ năng
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetTeacherSkillSuggestion(System.Guid,System.Guid)">
            <summary>
            Lấy danh sách các kỹ năng được giao gần với kỹ năng hiện tại
            </summary>
            <param name="skillId">Định danh kỹ năng đang làm</param>
            <param name="classroomId">Định danh lớp học</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetSkillsRecommendation(System.Nullable{System.Guid},System.Guid)">
            <summary>
            Lấy danh sách kỹ năng gợi ý của hệ thống
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.MarkExam(Hoclieu.Skills.MarkExamMongoRequest)">
            <summary>
            API chấm câu bài kiểm tra
            </summary>
            <param name="request"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.MarkCheckpoint(Hoclieu.Skills.MarkCheckpointMongoRequest)">
            <summary>
            API chấm câu tự luận của đề
            </summary>
            <param name="request"></param>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.CheckEditableBook(System.Guid)">
            <summary>
            Kiểm tra người dùng(biên tập viên) có quyền chỉnh sửa với kĩ năng hay không
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.CheckPermissionDoSkill(System.Guid)">
            <summary>
            API kiểm tra có quyền được làm skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.viewMobile(System.Guid)">
            <summary>
            Gửi tín hiệu báo view skill trên mobile
            </summary>
            <param name="skillId"></param>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.viewQuestionMobile(System.Guid,Hoclieu.Core.Dtos.Skill.SkillMobileViewRequest)">
            <summary>
            Gửi tín hiệu báo view câu hỏi của skill trên mobile
            </summary>
            <param name="skillTemplateDataId"></param>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetQuestionMobileAsync(System.Guid,System.Nullable{System.Guid},System.Nullable{System.Guid},Hoclieu.Core.Enums.ClientType)">
            <summary>
            Lấy câu hỏi lần lượt để rà soát trên Mobile
            </summary>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.TemplateStatistics">
            <summary>
            Lấy thống kê dạng bài sử dụng
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.DeleteSkillCheckpointCache(System.Guid,Hoclieu.Core.Enums.SkillCheckpointCacheType)">
            <summary>
            Xoá đề thi giáo viện
            </summary>
            <param name="Id">Định danh kĩ năng</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.UpdateSkillCheckpointCache(System.Guid,System.String,System.Nullable{System.Guid},System.Nullable{System.Guid})">
            <summary>
            Cập nhật đề thi giáo viện
            </summary>
            <param name="Id">Định danh kỹ năng</param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetNextOrPreviousSkillSection(System.Guid,System.Nullable{System.Guid})">
            <summary>
            get next and previous skill section by skill id
            </summary>
            return next skill section
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.AddNote(System.Guid,Hoclieu.Core.Dtos.Skill.NoteRequest)">
            <summary>
            Tạo ghi chú cho kỹ năng
            </summary>
            <param name="request">dữ liệu ghi chú</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.EditNote(System.Guid,Hoclieu.Core.Dtos.Skill.NoteRequest)">
            <summary>
            Sửa ghi chú cho kỹ năng
            </summary>
            <param name="request">dữ liệu ghi chú</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetSkillDetail(System.Guid)">
            <summary>
            Get skill detail
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetSkillComments(System.Guid,System.Guid)">
            <summary>
            Get NewDataQuestion comments
            </summary>
            <param name="newDataQuestionId"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.ReviewSkillForEditor(System.Guid)">
            <summary>
             Review question for editor
             </summary>
             <param name="id"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetAllCssOfSkills(System.Collections.Generic.List{System.Guid})">
            <summary>
            Get all css of skill by list skill id
            </summary>
            <param name="skillIds"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetAllCssOfSkillDetails(Hoclieu.Core.Dtos.TestBank.GetAllSkillCssRequest)">
            <summary>
            Get all css of skill by list skill id detail
            </summary>
            <param name="skillIds"></param>
            <returns></returns>
            <exception cref="T:System.ApplicationException"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetExerciseHistoryBySkillResultId(System.Guid,System.Boolean,System.Boolean)">
            <summary>
            Get history do exercise of student by skillresult id
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetExerciseStudentPractise(System.DateTime,System.DateTime)">
            <summary>
            Get list exercise sutdent practise
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.DuplicateSkillAsync(System.Guid)">
            <summary>
            Duplicate skill
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillsController.GetCount(System.String)">
            <summary>
            Lấy số lượng nộp bài thử ngày hôm nay
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Hoclieu.Controllers.SkillsController.GetQuestionHistory(System.Guid)" -->
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.Create(System.Collections.Generic.List{Hoclieu.SkillSuggestions.CreateSkillSuggestRequest})">
            <summary>
            Tạo mới bài tập được giao cho học sinh
            </summary>
            <param name="request">Dữ liệu bài tập được giao</param>
            <returns>Kết quả</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.Remove(System.Collections.Generic.List{System.Guid})">
            <summary>
            Xoá giao bài nhiều người
            </summary>
            <param name="requests">Danh sách định danh giao bài</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.RemoveAllClassroom(System.Guid,System.Guid,System.Guid)">
            <summary>
            Xoá giao bài nhiều người
            </summary>
            <param name="requests">Danh sách định danh giao bài</param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.GetByStudentAndSkill(System.Guid)">
            <summary>
            Lấy giao bài theo student và skillId
            </summary>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.UpdateResultDoExercise(Hoclieu.Core.Dtos.SkillSuggestion.UpdateSkillResultRequest)">
            <summary>
             Cập nhập lại kết quả làm bài của học sinh
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.CreateSkillResultAfterOpenModal(Hoclieu.Core.Dtos.SkillSuggestion.UpdateSkillResultRequest)">
            <summary>
            Tạo skillResult sau khi hiện pop up
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.GetAllSkillSuggestion(System.Guid,System.Nullable{System.Guid},System.Nullable{System.Guid})">
            <summary>
            Lấy thông tin các kỹ năng được gợi ý
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.GetSkillSuggestionById(System.Nullable{System.Guid},System.Nullable{System.Guid})">
            <summary>
            lấy thông tin skill suggestion
            </summary>
            <param name="skillSuggestionId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.CheckRevokeClassroomSuggestions(Hoclieu.Core.Dtos.TestBank.RevokeClassroomTestSuggestionRequest)">
            <summary>
            Lấy dữ liệu làm bài của học sinh
            </summary>
            <param name="classroomId"></param>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.RevokeClassroomSuggestions(Hoclieu.Core.Dtos.TestBank.RevokeClassroomTestSuggestionRequest)">
            <summary>
            Thu hồi giao bài cả lớp học
            </summary>
            <param name="classroomId"></param>
            <param name="skillId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillSuggestionController.GetStatisticSkillSuggestion(System.Guid,Hoclieu.SkillSuggestions.StatisticSkillSuggestionRequest)">
            <summary>
            Get statistic skill suggestion
            </summary>
            <returns></returns>
        </member>
        <member name="T:Hoclieu.Controllers.SkillQuestionDataDto">
             <summary>
            
             </summary>
        </member>
        <member name="P:Hoclieu.Controllers.SkillQuestionDataDto.SkillType">
             <summary>
            
             </summary>
        </member>
        <member name="P:Hoclieu.Controllers.SkillQuestionDataDto.SkillTemplateTitle">
             <summary>
            
             </summary>
        </member>
        <member name="P:Hoclieu.Controllers.SkillQuestionDataDto.SkillTemplateDataId">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.Create(Hoclieu.Skills.CreateSkillTeacherRequest)">
            <summary>
            Tạo mới kỹ năng cho giáo viên
            </summary>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.Publish(System.Guid)">
            <summary>
            Cập nhập kỹ năng xuất bản
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.RequestAddToBucket(System.Guid,Hoclieu.Skills.NoteForSubmittedSkillRequest)">
            <summary>
            Thêm yêu cầu cho Bộ CH vào kho
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.ApproveSkillToBucket(System.Guid,Hoclieu.Skills.NoteForProcessedSkillRequest)">
            <summary>
            Phê duyệt bộ CH vào kho Ngân hàng HL
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetApprovalsHistory(System.Guid)">
            <summary>
            Lấy thông tin lịch sử duyệt đề
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.RejectSkillToBucket(System.Guid,Hoclieu.Skills.NoteForProcessedSkillRequest)">
            <summary>
            Từ chối thêm Bộ CH vào kho Ngân hàng
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.RemoveApprovalSkillToBucket(System.Guid,Hoclieu.Skills.NoteForProcessedSkillRequest)">
            <summary>
            Bỏ duyệt đề trong kho câu hỏi
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.DeleteSkillFromBucket(System.Guid)">
            <summary>
            Xóa khỏi danh sách đề đã xử lý
            </summary>
            <param name="id"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.SearchPendingApprovalSkillTeachers(Hoclieu.Skills.SearchPendingSkillRequest)">
            <summary>
            Danh sách bộ câu hỏi đang đợi duyệt vào kho
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.FindMyHocLieu(Hoclieu.Skills.FindSkillRequest)">
             <summary>
            
             </summary>
             <param name="request"></param>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.FindMyHocLieuNew(Hoclieu.Skills.FindSkillRequest)">
            <summary>
            Hàm lấy danh sách kỹ năng của giáo viên
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CountAllMyHocLieu">
            <summary>
            Đếm hoc lieu cua toi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CheckSkillTeacherDone(System.Guid)">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CreateQuestionAsync(System.Guid,Hoclieu.Skills.CreateQuestionRequest)">
            <summary>
            Them moi cau hoi
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.AssignQuestionToSkill(System.Guid,Hoclieu.Skills.AssignQuestionRequest)">
            <summary>
            Gán câu hỏi đã có vào bộ câu hỏi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.AddListQuestionAsync(System.Guid,Hoclieu.Skills.CreateQuestionsRequest,System.Int32,System.String)">
            <summary>
            Thêm nhóm câu hỏi vào đề
            </summary>
            <param name="skillId"></param>
            <param name="request"></param>
            <param name="startNumericalOrder"></param>
            <param name="nameGroupAI"></param>
            <exception cref="T:System.ApplicationException"></exception>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetDataBySkillTemplateDataId(System.Guid)">
            <summary>
            Get data by skillTemplateDataId
            </summary>
            <param name="skillTemplateDataId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CreateOrUpdateExamMatrixCategories(System.Guid,Hoclieu.DataQuestions.SkillTemplateDataDto)">
            <summary>
            cập nhật category matrix
            </summary>
            <param name="id"></param>
            <param name="request"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CreateOrUpdateLabels(Hoclieu.DataQuestions.LabelSkilltemplateDataDto)">
            <summary>
             Thêm nhãn vào câu hỏi
            </summary>
            <param name="id"></param>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CreateOrUpdateListLabel(System.Guid,Hoclieu.DataQuestions.ListLabelSkillTemplateDataDto)">
            <summary>
            Thêm bộ nhãn câu hỏi
            </summary>
            <param name="skillTemplateDataId"></param>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetQuestionsForAdmin(Hoclieu.Domain.Skills.QuestionForAdmin)">
            <summary>
            Lấy danh sách câu hỏi theo kỹ năng cho admin
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CopySkill(System.Guid)">
            <summary>
            Sao chép kĩ năng được chia sẻ vào kĩ năng của giáo viên
            </summary>
            <param name="id"> Định danh chia sẻ</param>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.CheckPublishSkillOfMySkill(System.Guid)">
            <summary>
            Check kỹ năng đã publish hay chưa
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetQuestionNoAI(Hoclieu.Domain.Skills.QuestionToAIRequest)">
            <summary>
            Lấy câu hỏi lẻ theo kỹ năng
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetViewGroupQuestion(System.Guid,System.Guid)">
            <summary>
            View nhóm câu hỏi bộ
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetNewDataQuestion(System.Guid)">
            <summary>
            Lấy danh sách question đã thêm câu hỏi
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetNumberQuestionForTestBank(Hoclieu.Core.Dtos.TestBank.NumberQuestionRequest)">
             <summary>
             Get number question by skill teacher
             </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetNumberOfQuestionSetsFromAccount(Hoclieu.Core.Dtos.SkillTeacher.QuestionSetStatisticsRequest)">
            <summary>
            API lấy số lượng bộ câu hỏi của một tài khoản.
            </summary>
            <param name="request"></param>
            <returns>Số lượng câu hỏi và user</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeachersController.GetSkillTemplateStatistics(Hoclieu.Core.Dtos.SkillTeacher.SkillTemplateStatisticsRequest)">
            <summary>
            API lấy số lượng câu hỏi trong bộ câu hỏi.
            </summary>
            <param name="request"></param>
            <returns>Số lượng câu hỏi và user</returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeacherSharesController.GetHocLieuSharedWithMe(Hoclieu.Skills.FindSkillRequest)">
             <summary>
            
             </summary>
             <returns></returns>
        </member>
        <member name="M:Hoclieu.Controllers.SkillTeacherSharesController.CopySkillShare(System.Guid,System.String)">
            <summary>
            Sao chép kĩ năng được chia sẻ vào kĩ năng của giáo viên
            </summary>
            <param name="id"> Định danh chia sẻ</param>
            <exception cref="T:System.NullReferenceException"></exception>
        </member>
        <member name="T:Hoclieu.Controllers.StudyJournalController">
            <summary>
            Controller cho Nhật ký học
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.StudyJournalController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.StudyTrackingRepository,Hoclieu.Mongo.Service.MongoAnsweredQuestionRepository,Hoclieu.Mongo.Service.MongoSkillResultRepository)">
            <summary>
            Controller cho Nhật ký học
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.StudyJournalController.GetAnsweredQuestionAndTimeStudy(System.DateOnly,System.DateOnly)">
            <summary>
            Lấy danh sách câu hỏi đã trả lời và thời gian học trong khoảng ngày chỉ định.
            </summary>
            <param name="fromDate">Ngày bắt đầu (DateOnly).</param>
            <param name="toDate">Ngày kết thúc (DateOnly).</param>
            <returns>Trả về danh sách câu hỏi đã trả lời và thời gian học theo từng môn và từng ngày.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyJournalController.GetStudiedBooks(System.DateOnly,System.DateOnly)">
            <summary>
            Lấy ra list các sách đã học trong khoảng thời gian 
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.StudyJournalController.GetHistory(System.DateOnly,System.DateOnly)">
            <summary>
            Thống kê lịch sử làm skill hàng ngày dựa trên trackingData.
            </summary>
        </member>
        <member name="T:Hoclieu.Controllers.StudyPlanController">
            <summary>
            Controller cho mục tiêu hàng ngày
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.StudyPlans.StudyPlanRepository,Hoclieu.Mongo.Service.StudyLogsRepository,Microsoft.Extensions.Caching.Distributed.IDistributedCache)">
            <summary>
            Controller cho mục tiêu hàng ngày
            </summary>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.AddStudyPlan(Hoclieu.StudyPlans.AddStudyPlanRequest)">
            <summary>
            Thêm một mục tiêu hàng ngày mới cho người dùng.
            </summary>
            <param name="request">Thông tin mục tiêu hàng ngày cần thêm.</param>
            <returns>Kết quả thêm mục tiêu hàng ngày.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.UpdateStudyPlan(System.Guid,Hoclieu.StudyPlans.AddStudyPlanRequest)">
            <summary>
            Cập nhật một mục tiêu hàng ngày cho người dùng.
            </summary>
            <param name="id">ID của mục tiêu hàng ngày cần cập nhật.</param>
            <param name="request">Thông tin mục tiêu hàng ngày mới.</param>
            <returns>Kết quả cập nhật mục tiêu hàng ngày.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.GetCurrentStudyPlan">
            <summary>
            Lấy mục tiêu hàng ngày hiện tại của người dùng.
            </summary>
            <returns>Mục tiêu hàng ngày hiện tại.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.GetDayElapsed(System.Guid)">
            <summary>
            Trả về thống kê số ngày đã học của một mục tiêu học tập (StudyPlan) theo người dùng hiện tại.
            Thống kê gồm tổng số ngày, số ngày bỏ học, học không đủ và học đầy đủ.
            </summary>
            <param name="id">ID của mục tiêu học tập (StudyPlan).</param>
            <returns>Thống kê số ngày đã học theo trạng thái.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.GetDayStatuses(System.Guid,System.DateOnly,System.DateOnly)">
            <summary>
            Trả về danh sách trạng thái từng ngày học trong khoảng thời gian chỉ định.
            </summary>
            <param name="id">ID của mục tiêu học tập (StudyPlan).</param>
            <param name="startDate">Ngày bắt đầu thống kê (yyyy-MM-dd).</param>
            <param name="endDate">Ngày kết thúc thống kê (yyyy-MM-dd, không bao gồm).</param>
            <returns>Danh sách trạng thái học tập theo từng ngày.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.StopStudyPlanById(System.Guid)">
            <summary>
            Dừng mục tiêu học tập hiện tại của người dùng.
            </summary>
            <param name="id">ID của mục tiêu học tập.</param>
            <returns>Kết quả dừng mục tiêu.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.GetEndedStudyPlans">
            <summary>
            Lấy danh sách các mục tiêu đã kết thúc.
            </summary>
            <returns>Danh sách mục tiêu đã kết thúc.</returns>
        </member>
        <member name="M:Hoclieu.Controllers.StudyPlanController.DeleteStudyPlanById(System.Guid)">
            <summary>
            Xóa mục tiêu hàng ngày hiện tại của người dùng (chỉ khi còn hoạt động).
            </summary>
        </member>
        <member name="T:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.#ctor(Hoclieu.Services.Competition.CompetitionService,Hoclieu.EntityFrameworkCore.HoclieuDbContext,Hoclieu.Mongo.Service.ContestResultRepository,Hoclieu.Services.Competition.TroubleShootingService)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.CreateCompetition(CompetitionCreateRequest)">
            <summary>
            Create a new competition
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.UpdateCompetition(System.Int32,Hoclieu.Core.Dtos.Competition.CompetitionUpdateRequest)">
            <summary>
            Update a competition
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.DeleteCompetition(System.Int32)">
            <summary>
            Delete a competition
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.GetCompetitions(System.Int32,System.Int32)">
            <summary>
            Get all competitions
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.AddCandidate(Hoclieu.Core.Dtos.Competition.CompetitionAddCandidateRequest)">
            <summary>
            Add a candidate to a competition
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.RemoveCandidate(Hoclieu.Core.Dtos.Competition.CompetitionRemoveCandidateRequest)">
            <summary>
            Add a candidate to a competition
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.GetCompetitionExam(System.Int32,System.Int32,System.Int32)">
            <summary>
            Get VRExams
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.CreateCompetitionExam(Hoclieu.Core.Dtos.Competition.CompetitionExamCreateOrUpdateRequest)">
            <summary>
            Create a new VRExam
            </summary>
            <returns></returns>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.UpdateCompetitionExam(System.Int32,Hoclieu.Core.Dtos.Competition.CompetitionExamCreateOrUpdateRequest)">
            <summary>
            Update a VRExam
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.DeleteCompetitionExam(System.Int32)">
            <summary>
            Delete a VRExam
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.GetAllCompetitionCandidate(System.Int32,System.Int32,System.Int32)">
            <summary>
            Get competitionCandidate list
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.UpsertExamCandidate(System.Int32,Hoclieu.Core.Dtos.Competition.UpsertExamCandidateRequest)">
            <summary>
            Update or insert a candidate
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.ImportExamCandidate(System.Int32,Hoclieu.Core.Dtos.Competition.ImportExamCandidateRequest)">
            <summary>
            Preview or import candidates from sheet
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.Competition.AdminCompetitionsController.RemoveExamCandidate(System.Int32)">
            <summary>
            Delete a candidate
            </summary>
        </member>
        <member name="T:Hoclieu.EntityFrameworkCore.PaymentProduct.PaymentProductController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProduct.PaymentProductController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProduct.PaymentProductController.GetPaymentProducts(System.Int32,System.Int32)">
            <summary>
            Lấy ra payment product phân trang
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProduct.PaymentProductController.GetAll">
            <summary>
            Lấy ra tất cả payment product
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProduct.PaymentProductController.UpsertPaymentProduct(Hoclieu.Core.Dtos.PaymentProduct.UpsertPaymentProductRequest)">
            <summary>
            Upsert PaymentProduct
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProduct.PaymentProductController.DeletePaymentProduct(System.Guid)">
            <summary>
            Delete PaymentProduct by Id
            </summary>
            <param name="id">Id of the PaymentProduct</param>
            <returns>ActionResult</returns>
        </member>
        <member name="T:Hoclieu.EntityFrameworkCore.PaymentProductDiscount.PaymentProductDiscountController">
             <summary>
            
             </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProductDiscount.PaymentProductDiscountController.#ctor(Hoclieu.EntityFrameworkCore.HoclieuDbContext)">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProductDiscount.PaymentProductDiscountController.GetAllPaymentProductDiscount(System.String,System.Nullable{Hoclieu.Core.Enums.PaymentProduct.DiscountType},System.Nullable{Hoclieu.Core.Enums.PaymentProduct.DiscountMode},System.Nullable{Hoclieu.Core.Enums.PaymentProduct.PaymentProductDiscountStatus},System.Int32,System.Int32)">
            <summary>
            Get all PaymentProductDiscount
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProductDiscount.PaymentProductDiscountController.UpsertPaymentProductDiscount(Hoclieu.Core.Dtos.PaymentProduct.UpsertPaymentProductDiscountRequest)">
            <summary>
            Upsert PaymentProductDiscount
            </summary>
        </member>
        <member name="M:Hoclieu.EntityFrameworkCore.PaymentProductDiscount.PaymentProductDiscountController.DeletePaymentProductDiscount(System.Guid)">
            <summary>
            Delete PaymentProductDiscount by Id
            </summary>
            <param name="id">Id of the PaymentProductDiscount</param>
            <returns>ActionResult</returns>
        </member>
        <member name="T:Hoclieu.Startup">
            <summary>
            class Startup
            </summary>
        </member>
        <member name="M:Hoclieu.Startup.#ctor(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            constructor Startup
            </summary>
        </member>
        <member name="P:Hoclieu.Startup.Configuration">
            <summary>
            Configuration
            </summary>
        </member>
        <member name="M:Hoclieu.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            This method gets called by the runtime. Use this method to add services to the container.
            </summary>
        </member>
        <member name="M:Hoclieu.Startup.Configure(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
            </summary>
        </member>
    </members>
</doc>
